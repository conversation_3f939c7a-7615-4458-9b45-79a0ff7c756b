# 使用阿里云镜像
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.enableJetifier=true

# 阿里云镜像
repositoriesMode=fail_on_project_repos
repositoryUrl=https://maven.aliyun.com/repository/public
repositoriesUrl=https://maven.aliyun.com/repository/public
repositoriesUsername=
repositoriesPassword=

# 阿里云镜像配置
systemProp.org.gradle.internal.publish.checksums.insecure=true
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=
