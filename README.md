# 锁屏软件 (LockScreen App)

这是一个纯本地运行的锁屏软件，提供自定义锁屏背景和两种解锁方式，支持安全邮箱设置和解锁失败告警功能。

## 功能特点

1. 自定义锁屏背景图片
2. 两种解锁方式：
   - 九宫格点位界面 + 密码解锁：界面显示九宫格，但解锁需要按照设置的密码顺序点击对应位置
   - 密码输入界面 + 图案解锁：界面显示密码输入框，但解锁需要按照设置的九宫格图案顺序
3. 锁屏设置页面
4. 安全邮箱设置功能
5. 解锁失败告警功能（连续三次解锁失败后发送包含照片和位置信息的告警邮件）

## 项目结构

```
LockScreenApp/
├── app/                      # 应用主模块
│   ├── src/                  # 源代码
│   │   ├── main/            # 主要代码
│   │   │   ├── java/        # Java代码
│   │   │   │   └── com/lockscreen/app/  # 包名
│   │   │   │       ├── activities/      # 活动类
│   │   │   │       ├── services/        # 服务类
│   │   │   │       ├── receivers/       # 广播接收器
│   │   │   │       ├── utils/           # 工具类
│   │   │   │       └── views/           # 自定义视图
│   │   │   ├── res/         # 资源文件
│   │   │   │   ├── drawable/           # 图像资源
│   │   │   │   ├── layout/             # 布局文件
│   │   │   │   ├── values/             # 值资源
│   │   │   │   └── xml/                # XML配置
│   │   │   └── AndroidManifest.xml     # 应用清单
│   │   └── test/            # 测试代码
│   ├── build.gradle         # 应用模块构建脚本
│   └── proguard-rules.pro   # ProGuard规则
├── gradle/                  # Gradle包装器
├── build.gradle             # 项目构建脚本
├── settings.gradle          # 项目设置
├── gradlew                  # Gradle包装器脚本(Unix)
├── gradlew.bat              # Gradle包装器脚本(Windows)
└── README.md                # 项目说明
```

## 功能列表

1. **锁屏服务**
   - 启动/停止锁屏服务
   - 锁屏界面显示
   - 锁屏状态管理

2. **锁屏界面**
   - 自定义背景图片显示
   - 时间和日期显示
   - 解锁界面切换

3. **九宫格密码解锁**
   - 九宫格界面绘制
   - 密码输入处理
   - 密码验证

4. **图案解锁**
   - 图案绘制
   - 图案验证

5. **设置界面**
   - 锁屏开关
   - 背景图片选择
   - 解锁方式选择
   - 密码设置
   - 图案设置
   - 安全邮箱设置
   - 测试邮件发送

6. **系统集成**
   - 开机自启动
   - 系统锁屏替换

## 构建与安装

使用项目根目录下的打包脚本进行构建：

```bash
./build_apk.sh
```

构建完成后，APK文件将位于`app/build/outputs/apk/`目录下。

## 最近优化

### 安全邮箱设置功能

在设置页面添加了安全邮箱设置功能，包括以下设置项：

- **启用邮箱安全告警**：开关选项，控制是否启用安全邮件功能
- **安全邮箱地址**：接收告警邮件的邮箱地址
- **发件人邮箱**：用于发送告警邮件的邮箱地址
- **邮箱密码**：发件人邮箱的密码
- **SMTP服务器**：发件人邮箱的SMTP服务器地址
- **SMTP端口**：SMTP服务器端口
- **测试邮件发送**：测试邮件发送功能是否正常

### 解锁失败告警功能

实现了在解锁失败三次后，自动发送包含前置摄像头拍摄的照片和手机位置信息的告警邮件功能：

- 添加了解锁失败计数功能
- 在密码和图案解锁失败时增加计数
- 当失败次数达到3次时，发送安全邮件
- 邮件包含前置摄像头拍摄的照片和手机位置信息

### 编译优化

优化了编译速度，使用国内源下载依赖：

- 在`build.gradle`文件中添加了阿里云Maven镜像源
- 修改了`build.sh`脚本，添加了国内镜像源配置
- 添加了`--no-daemon`和`--parallel`参数提高编译效率