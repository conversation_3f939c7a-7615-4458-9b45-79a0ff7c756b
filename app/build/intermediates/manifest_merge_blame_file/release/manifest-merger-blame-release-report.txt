1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lockscreen.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:6:5-81
12-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
13-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:7:5-75
13-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
14-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:8:5-78
14-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:8:22-75
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:9:5-80
15-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:10:5-81
16-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.CAMERA" />
17-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:11:5-65
17-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:11:22-62
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:12:5-79
18-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:12:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:13:5-81
19-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:13:22-78
20    <uses-permission android:name="android.permission.INTERNET" />
20-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:14:5-67
20-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:14:22-64
21
22    <application
22-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:16:5-63:19
23        android:allowBackup="true"
23-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:17:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:24:18-86
25        android:label="@string/app_name"
25-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:18:9-41
26        android:supportsRtl="true"
26-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:19:9-35
27        android:theme="@style/AppTheme" >
27-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:20:9-40
28
29        <!-- 主活动 -->
30        <activity
30-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:23:9-30:20
31            android:name="com.lockscreen.app.activities.MainActivity"
31-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:24:13-52
32            android:exported="true" >
32-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:25:13-36
33            <intent-filter>
33-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:26:13-29:29
34                <action android:name="android.intent.action.MAIN" />
34-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:27:17-69
34-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:27:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:28:17-77
36-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:28:27-74
37            </intent-filter>
38        </activity>
39
40        <!-- 设置活动 -->
41        <activity
41-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:33:9-36:69
42            android:name="com.lockscreen.app.activities.SettingsActivity"
42-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:34:13-56
43            android:label="@string/settings_title"
43-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:35:13-51
44            android:parentActivityName="com.lockscreen.app.activities.MainActivity" />
44-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:36:13-66
45
46        <!-- 锁屏活动 -->
47        <activity
47-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:39:9-45:54
48            android:name="com.lockscreen.app.activities.LockScreenActivity"
48-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:40:13-58
49            android:excludeFromRecents="true"
49-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:41:13-46
50            android:launchMode="singleInstance"
50-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:42:13-48
51            android:screenOrientation="portrait"
51-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:43:13-49
52            android:showOnLockScreen="true"
52-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:44:13-44
53            android:theme="@style/LockScreenTheme" />
53-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:45:13-51
54
55        <!-- 锁屏服务 -->
56        <service
56-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:48:9-51:40
57            android:name="com.lockscreen.app.services.LockScreenService"
57-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:49:13-55
58            android:enabled="true"
58-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:50:13-35
59            android:exported="false" />
59-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:51:13-37
60
61        <!-- 开机启动接收器 -->
62        <receiver
62-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:54:9-61:20
63            android:name="com.lockscreen.app.receivers.BootCompletedReceiver"
63-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:55:13-60
64            android:enabled="true"
64-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:56:13-35
65            android:exported="true" >
65-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:57:13-36
66            <intent-filter>
66-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:58:13-60:29
67                <action android:name="android.intent.action.BOOT_COMPLETED" />
67-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:59:17-79
67-->/home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:59:25-76
68            </intent-filter>
69        </receiver>
70
71        <activity
71-->[com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:23:9-26:75
72            android:name="com.google.android.gms.common.api.GoogleApiActivity"
72-->[com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:24:13-79
73            android:exported="false"
73-->[com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:25:13-37
74            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
74-->[com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:26:13-72
75
76        <meta-data
76-->[com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:23:9-25:69
77            android:name="com.google.android.gms.version"
77-->[com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:24:13-58
78            android:value="@integer/google_play_services_version" />
78-->[com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:25:13-66
79
80        <provider
80-->[androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:25:9-29:43
81            android:name="androidx.lifecycle.ProcessLifecycleOwnerInitializer"
81-->[androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:26:13-79
82            android:authorities="com.lockscreen.app.lifecycle-process"
82-->[androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:27:13-69
83            android:exported="false"
83-->[androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:28:13-37
84            android:multiprocess="true" />
84-->[androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:29:13-40
85    </application>
86
87</manifest>
