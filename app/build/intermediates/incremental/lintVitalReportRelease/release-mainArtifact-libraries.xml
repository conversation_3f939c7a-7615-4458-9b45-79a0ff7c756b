<libraries>
  <library
      name="com.google.android.material:material:1.4.0@aar"
      jars="/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.4.0"
      folder="/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.4@aar"
      jars="/root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.4"
      folder="/root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.1.1@aar"
      jars="/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/jars/classes.jar"
      resolved="androidx.preference:preference:1.1.1"
      folder="/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.3.0@aar"
      jars="/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.3.0"
      folder="/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="/root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.sun.mail:android-mail:1.6.7@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/com.sun.mail/android-mail/1.6.7/7b4b267c5dbcf164b7ed1e0535797e247f580b0c/android-mail-1.6.7.jar"
      resolved="com.sun.mail:android-mail:1.6.7"/>
  <library
      name="com.sun.mail:android-activation:1.6.7@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/com.sun.mail/android-activation/1.6.7/be2313e85510e68188c58f52eb7e5c043c3c005c/android-activation-1.6.7.jar"
      resolved="com.sun.mail:android-activation:1.6.7"/>
  <library
      name="com.google.android.gms:play-services-location:18.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-location:18.0.0"
      folder="/root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/0b52811f51de9a0dcbbefeb2f6298972/transformed/jetified-viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/root/.gradle/caches/transforms-3/0b52811f51de9a0dcbbefeb2f6298972/transformed/jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:17.5.0@aar"
      jars="/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-base:17.5.0"
      folder="/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-places-placereport:17.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-places-placereport:17.0.0"
      folder="/root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:17.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-tasks:17.2.0"
      folder="/root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:17.5.0@aar"
      jars="/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/jars/classes.jar"
      resolved="com.google.android.gms:play-services-basement:17.5.0"
      folder="/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.4@aar"
      jars="/root/.gradle/caches/transforms-3/c119dff1bd73c5f3292699d4f585d352/transformed/fragment-1.3.4/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.4"
      folder="/root/.gradle/caches/transforms-3/c119dff1bd73c5f3292699d4f585d352/transformed/fragment-1.3.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.2.3@aar"
      jars="/root/.gradle/caches/transforms-3/2acd013e9e3066b2c6d9363d50389286/transformed/jetified-activity-1.2.3/jars/classes.jar"
      resolved="androidx.activity:activity:1.2.3"
      folder="/root/.gradle/caches/transforms-3/2acd013e9e3066b2c6d9363d50389286/transformed/jetified-activity-1.2.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.3.0@aar"
      jars="/root/.gradle/caches/transforms-3/3c2a851d967e92986ffead089daaf9a1/transformed/jetified-appcompat-resources-1.3.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.3.0"
      folder="/root/.gradle/caches/transforms-3/3c2a851d967e92986ffead089daaf9a1/transformed/jetified-appcompat-resources-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/e89f067bfa86e4a18d078e9c4406d0ea/transformed/drawerlayout-1.0.0/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="/root/.gradle/caches/transforms-3/e89f067bfa86e4a18d078e9c4406d0ea/transformed/drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/9dc63fb162b011a8c708a21bc0d79bd4/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/root/.gradle/caches/transforms-3/9dc63fb162b011a8c708a21bc0d79bd4/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/65a844cc5dd39a5eb70353a038d57f2f/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/root/.gradle/caches/transforms-3/65a844cc5dd39a5eb70353a038d57f2f/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/bedc0ebe5eb899b1eb26399ed2312b70/transformed/recyclerview-1.1.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="/root/.gradle/caches/transforms-3/bedc0ebe5eb899b1eb26399ed2312b70/transformed/recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/170b8a2cb72f855a00f5c71bebfe83bf/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/root/.gradle/caches/transforms-3/170b8a2cb72f855a00f5c71bebfe83bf/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/6c376253eb6e129894cb7c65233af65d/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/root/.gradle/caches/transforms-3/6c376253eb6e129894cb7c65233af65d/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/663879c1f16d1f2371d9c3017510e1d9/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/root/.gradle/caches/transforms-3/663879c1f16d1f2371d9c3017510e1d9/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/b268d2f769a8867aa56bdc1b2aed6d1c/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/root/.gradle/caches/transforms-3/b268d2f769a8867aa56bdc1b2aed6d1c/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/9f696acb8d2e8dca6b1be9ed5088a295/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/root/.gradle/caches/transforms-3/9f696acb8d2e8dca6b1be9ed5088a295/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/65b33b9091dda4ea3e48406dacf435a8/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/root/.gradle/caches/transforms-3/65b33b9091dda4ea3e48406dacf435a8/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/e3d5d9db1d8847be9a6ab85c036cf2a5/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/root/.gradle/caches/transforms-3/e3d5d9db1d8847be9a6ab85c036cf2a5/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.5.0@aar"
      jars="/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/jars/classes.jar"
      resolved="androidx.core:core:1.5.0"
      folder="/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/39585d7ff596c3f6c695fa13a4bfa70a/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/root/.gradle/caches/transforms-3/39585d7ff596c3f6c695fa13a4bfa70a/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"
      jars="/root/.gradle/caches/transforms-3/b9abe9dc14f11c07c7b65d00dcfb4b29/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1"
      folder="/root/.gradle/caches/transforms-3/b9abe9dc14f11c07c7b65d00dcfb4b29/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/bae0b629c4a2b1864a7e704a80e3f04a/transformed/jetified-savedstate-1.1.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.1.0"
      folder="/root/.gradle/caches/transforms-3/bae0b629c4a2b1864a7e704a80e3f04a/transformed/jetified-savedstate-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/918aaabc1355d10d38c7a3fd8d9d1ca9/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/root/.gradle/caches/transforms-3/918aaabc1355d10d38c7a3fd8d9d1ca9/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.2.0"
      folder="/root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/2980610ab32c7c4a636688c9b7a85a97/transformed/jetified-lifecycle-service-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.2.0"
      folder="/root/.gradle/caches/transforms-3/2980610ab32c7c4a636688c9b7a85a97/transformed/jetified-lifecycle-service-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"
      jars="/root/.gradle/caches/transforms-3/b7836514ac2d7634e9557935af5a3d44/transformed/lifecycle-runtime-2.3.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.3.1"
      folder="/root/.gradle/caches/transforms-3/b7836514ac2d7634e9557935af5a3d44/transformed/lifecycle-runtime-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.2.0@aar"
      jars="/root/.gradle/caches/transforms-3/48fb1aacf8914590bd292c92bc1a142b/transformed/lifecycle-livedata-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.2.0"
      folder="/root/.gradle/caches/transforms-3/48fb1aacf8914590bd292c92bc1a142b/transformed/lifecycle-livedata-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/root/.gradle/caches/transforms-3/f2b4b7bd9e585b355e40553c3b3c443b/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/root/.gradle/caches/transforms-3/f2b4b7bd9e585b355e40553c3b3c443b/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"
      jars="/root/.gradle/caches/transforms-3/fd2aa8afb289030042fb0f20628a70ff/transformed/lifecycle-livedata-core-2.3.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.3.1"
      folder="/root/.gradle/caches/transforms-3/fd2aa8afb289030042fb0f20628a70ff/transformed/lifecycle-livedata-core-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.3.1@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.3.1/fc466261d52f4433863642fb40d12441ae274a98/lifecycle-common-2.3.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.3.1"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"
      jars="/root/.gradle/caches/transforms-3/468a5b0895a944c02bd90f36de604649/transformed/lifecycle-viewmodel-2.3.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.3.1"
      folder="/root/.gradle/caches/transforms-3/468a5b0895a944c02bd90f36de604649/transformed/lifecycle-viewmodel-2.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/f2dea4af95c493a893bccf4ef1f4f6f0/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/root/.gradle/caches/transforms-3/f2dea4af95c493a893bccf4ef1f4f6f0/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/984ec6733cf30892d757d05b4951f391/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/root/.gradle/caches/transforms-3/984ec6733cf30892d757d05b4951f391/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/583e60085bf9160870b0d0972848fafd/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/root/.gradle/caches/transforms-3/583e60085bf9160870b0d0972848fafd/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/431e5abf6be8c49ace162b4d492e1b23/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/root/.gradle/caches/transforms-3/431e5abf6be8c49ace162b4d492e1b23/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.2.0@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.2.0/57136ff68ee784c6e19db34ed4a175338fadfde1/annotation-1.2.0.jar"
      resolved="androidx.annotation:annotation:1.2.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/a0583fe1e38e86f99f02f37e6672f112/transformed/jetified-annotation-experimental-1.0.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.0.0"
      folder="/root/.gradle/caches/transforms-3/a0583fe1e38e86f99f02f37e6672f112/transformed/jetified-annotation-experimental-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.4@jar"
      jars="/root/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.4/1f001d7db280a89a6c26b26a66eb064bb6d5efeb/constraintlayout-solver-2.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.4"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/root/.gradle/caches/transforms-3/cb8d1266c5d00603bdadd8ca862b0019/transformed/jetified-tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/root/.gradle/caches/transforms-3/cb8d1266c5d00603bdadd8ca862b0019/transformed/jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
