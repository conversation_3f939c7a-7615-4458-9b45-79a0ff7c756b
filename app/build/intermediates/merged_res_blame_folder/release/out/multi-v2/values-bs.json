{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-bs/values-bs.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "332,453,550,657,743,847,969,1054,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2162,2265,2369,2470,2575,2689,2792,2961,10493", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "448,545,652,738,842,964,1049,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2157,2260,2364,2465,2570,2684,2787,2956,3052,10575"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-bs/values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "10580", "endColumns": "100", "endOffsets": "10676"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,364,460,586,667,733,825,902,965,1073,1139,1195,1266,1326,1380,1499,1556,1618,1672,1747,1871,1959,2042,2157,2242,2328,2416,2483,2549,2623,2701,2788,2860,2937,3010,3080,3173,3245,3337,3433,3507,3583,3679,3732,3799,3886,3973,4035,4099,4162,4270,4372,4473,4578", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "277,359,455,581,662,728,820,897,960,1068,1134,1190,1261,1321,1375,1494,1551,1613,1667,1742,1866,1954,2037,2152,2237,2323,2411,2478,2544,2618,2696,2783,2855,2932,3005,3075,3168,3240,3332,3428,3502,3578,3674,3727,3794,3881,3968,4030,4094,4157,4265,4367,4468,4573,4653"}, "to": {"startLines": "2,34,35,36,37,57,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3057,3139,3235,3361,6195,6348,6440,6517,6580,6688,6754,6810,6881,6941,6995,7114,7171,7233,7287,7362,7486,7574,7657,7772,7857,7943,8031,8098,8164,8238,8316,8403,8475,8552,8625,8695,8788,8860,8952,9048,9122,9198,9294,9347,9414,9501,9588,9650,9714,9777,9885,9987,10088,10275", "endLines": "6,34,35,36,37,57,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,108", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "327,3134,3230,3356,3437,6256,6435,6512,6575,6683,6749,6805,6876,6936,6990,7109,7166,7228,7282,7357,7481,7569,7652,7767,7852,7938,8026,8093,8159,8233,8311,8398,8470,8547,8620,8690,8783,8855,8947,9043,9117,9193,9289,9342,9409,9496,9583,9645,9709,9772,9880,9982,10083,10188,10350"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-bs/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,541,665,773,982,1106,1244,1371,1584,1689,1881,2006,2215,2387,2481,2569", "endColumns": "103,191,123,107,208,123,137,126,212,104,191,124,208,171,93,87,109", "endOffsets": "348,540,664,772,981,1105,1243,1370,1583,1688,1880,2005,2214,2386,2480,2568,2678"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3442,3550,3742,3870,3982,4191,4319,4461,4792,5005,5114,5306,5435,5644,5820,5918,6010", "endColumns": "107,191,127,111,208,127,141,130,212,108,191,128,208,175,97,91,113", "endOffsets": "3545,3737,3865,3977,4186,4314,4456,4587,5000,5109,5301,5430,5639,5815,5913,6005,6119"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-bs/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "199", "endOffsets": "446"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4592", "endColumns": "199", "endOffsets": "4787"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,345,483,652,739", "endColumns": "70,86,81,137,168,86,82", "endOffsets": "171,258,340,478,647,734,817"}, "to": {"startLines": "56,58,107,109,112,113,114", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6124,6261,10193,10355,10681,10850,10937", "endColumns": "70,86,81,137,168,86,82", "endOffsets": "6190,6343,10270,10488,10845,10932,11015"}}]}]}