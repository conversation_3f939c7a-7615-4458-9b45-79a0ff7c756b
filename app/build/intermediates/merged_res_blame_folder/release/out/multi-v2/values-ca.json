{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-ca/values-ca.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-ca/values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10709", "endColumns": "100", "endOffsets": "10805"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-ca/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "193", "endOffsets": "440"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4564", "endColumns": "193", "endOffsets": "4753"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-ca/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,348,532,657,764,973,1098,1247,1380,1610,1717,1907,2036,2259,2443,2534,2628", "endColumns": "102,183,124,106,208,124,148,132,229,106,189,128,222,183,90,93,111", "endOffsets": "347,531,656,763,972,1097,1246,1379,1609,1716,1906,2035,2258,2442,2533,2627,2739"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3405,3512,3696,3825,3936,4145,4274,4427,4758,4988,5099,5289,5422,5645,5833,5928,6026", "endColumns": "106,183,128,110,208,128,152,136,229,110,189,132,222,187,94,97,115", "endOffsets": "3507,3691,3820,3931,4140,4269,4422,4559,4983,5094,5284,5417,5640,5828,5923,6021,6137"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-ca/values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,317,418,546,630,695,792,872,937,1032,1104,1166,1242,1305,1362,1483,1541,1602,1659,1739,1876,1963,2047,2156,2234,2313,2402,2469,2535,2613,2694,2782,2860,2937,3011,3090,3180,3272,3364,3465,3539,3621,3722,3772,3838,3930,4017,4079,4143,4206,4329,4432,4536,4642", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,87,100,127,83,64,96,79,64,94,71,61,75,62,56,120,57,60,56,79,136,86,83,108,77,78,88,66,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,85", "endOffsets": "224,312,413,541,625,690,787,867,932,1027,1099,1161,1237,1300,1357,1478,1536,1597,1654,1734,1871,1958,2042,2151,2229,2308,2397,2464,2530,2608,2689,2777,2855,2932,3006,3085,3175,3267,3359,3460,3534,3616,3717,3767,3833,3925,4012,4074,4138,4201,4324,4427,4531,4637,4723"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3004,3092,3193,3321,6211,6372,6469,6549,6614,6709,6781,6843,6919,6982,7039,7160,7218,7279,7336,7416,7553,7640,7724,7833,7911,7990,8079,8146,8212,8290,8371,8459,8537,8614,8688,8767,8857,8949,9041,9142,9216,9298,9399,9449,9515,9607,9694,9756,9820,9883,10006,10109,10213,10398", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,87,100,127,83,64,96,79,64,94,71,61,75,62,56,120,57,60,56,79,136,86,83,108,77,78,88,66,65,77,80,87,77,76,73,78,89,91,91,100,73,81,100,49,65,91,86,61,63,62,122,102,103,105,85", "endOffsets": "274,3087,3188,3316,3400,6271,6464,6544,6609,6704,6776,6838,6914,6977,7034,7155,7213,7274,7331,7411,7548,7635,7719,7828,7906,7985,8074,8141,8207,8285,8366,8454,8532,8609,8683,8762,8852,8944,9036,9137,9211,9293,9394,9444,9510,9602,9689,9751,9815,9878,10001,10104,10208,10314,10479"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,349,492,661,748", "endColumns": "68,95,78,142,168,86,80", "endOffsets": "169,265,344,487,656,743,824"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6142,6276,10319,10484,10810,10979,11066", "endColumns": "68,95,78,142,168,86,80", "endOffsets": "6206,6367,10393,10622,10974,11061,11142"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-ca/values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "279,402,507,614,697,803,929,1013,1092,1183,1276,1369,1464,1562,1655,1748,1842,1933,2024,2105,2216,2324,2422,2532,2637,2745,2905,10627", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "397,502,609,692,798,924,1008,1087,1178,1271,1364,1459,1557,1650,1743,1837,1928,2019,2100,2211,2319,2417,2527,2632,2740,2900,2999,10704"}}]}]}