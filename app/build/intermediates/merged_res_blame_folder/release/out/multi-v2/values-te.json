{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-te/values-te.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-te/values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,317,421,537,626,692,786,853,915,1008,1076,1139,1213,1278,1332,1453,1510,1572,1626,1705,1833,1921,2013,2128,2208,2290,2378,2445,2511,2586,2664,2754,2827,2903,2984,3053,3158,3235,3326,3419,3493,3570,3662,3717,3783,3867,3953,4016,4081,4145,4255,4367,4466,4585", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "226,312,416,532,621,687,781,848,910,1003,1071,1134,1208,1273,1327,1448,1505,1567,1621,1700,1828,1916,2008,2123,2203,2285,2373,2440,2506,2581,2659,2749,2822,2898,2979,3048,3153,3230,3321,3414,3488,3565,3657,3712,3778,3862,3948,4011,4076,4140,4250,4362,4461,4580,4663"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3023,3109,3213,3329,6168,6321,6415,6482,6544,6637,6705,6768,6842,6907,6961,7082,7139,7201,7255,7334,7462,7550,7642,7757,7837,7919,8007,8074,8140,8215,8293,8383,8456,8532,8613,8682,8787,8864,8955,9048,9122,9199,9291,9346,9412,9496,9582,9645,9710,9774,9884,9996,10095,10292", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "276,3104,3208,3324,3413,6229,6410,6477,6539,6632,6700,6763,6837,6902,6956,7077,7134,7196,7250,7329,7457,7545,7637,7752,7832,7914,8002,8069,8135,8210,8288,8378,8451,8527,8608,8677,8782,8859,8950,9043,9117,9194,9286,9341,9407,9491,9577,9640,9705,9769,9879,9991,10090,10209,10370"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2249,2364,2467,2582,2744,2847", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,98,114,102,114,161,102,82", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2244,2359,2462,2577,2739,2842,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "281,398,510,621,711,816,935,1013,1089,1180,1273,1368,1462,1562,1655,1750,1845,1936,2027,2110,2224,2326,2425,2540,2643,2758,2920,10521", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,98,114,102,114,161,102,82", "endOffsets": "393,505,616,706,811,930,1008,1084,1175,1268,1363,1457,1557,1650,1745,1840,1931,2022,2105,2219,2321,2420,2535,2638,2753,2915,3018,10599"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-te/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "198", "endOffsets": "445"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4552", "endColumns": "198", "endOffsets": "4746"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,343,489,658,745", "endColumns": "72,86,77,145,168,86,83", "endOffsets": "173,260,338,484,653,740,824"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6095,6234,10214,10375,10705,10874,10961", "endColumns": "72,86,77,145,168,86,83", "endOffsets": "6163,6316,10287,10516,10869,10956,11040"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-te/values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10604", "endColumns": "100", "endOffsets": "10700"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,353,539,667,780,976,1099,1230,1355,1563,1673,1857,1983,2193,2382,2472,2568", "endColumns": "107,185,127,112,195,122,130,124,207,109,183,125,209,188,89,95,106", "endOffsets": "352,538,666,779,975,1098,1229,1354,1562,1672,1856,1982,2192,2381,2471,2567,2674"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3418,3530,3716,3848,3965,4161,4288,4423,4751,4959,5073,5257,5387,5597,5790,5884,5984", "endColumns": "111,185,131,116,195,126,134,128,207,113,183,129,209,192,93,99,110", "endOffsets": "3525,3711,3843,3960,4156,4283,4418,4547,4954,5068,5252,5382,5592,5785,5879,5979,6090"}}]}]}