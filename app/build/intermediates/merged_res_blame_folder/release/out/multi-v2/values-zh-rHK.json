{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,359,466,539,601,679,738,796,874,931,987,1046,1104,1158,1244,1300,1358,1412,1477,1570,1644,1722,1812,1875,1938,2015,2082,2148,2212,2281,2356,2417,2488,2555,2615,2695,2758,2841,2926,3000,3065,3141,3189,3253,3329,3407,3469,3533,3596,3676,3751,3827,3903", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "197,264,354,461,534,596,674,733,791,869,926,982,1041,1099,1153,1239,1295,1353,1407,1472,1565,1639,1717,1807,1870,1933,2010,2077,2143,2207,2276,2351,2412,2483,2550,2610,2690,2753,2836,2921,2995,3060,3136,3184,3248,3324,3402,3464,3528,3591,3671,3746,3822,3898,3967"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2813,2880,2970,3077,5531,5674,5752,5811,5869,5947,6004,6060,6119,6177,6231,6317,6373,6431,6485,6550,6643,6717,6795,6885,6948,7011,7088,7155,7221,7285,7354,7429,7490,7561,7628,7688,7768,7831,7914,7999,8073,8138,8214,8262,8326,8402,8480,8542,8606,8669,8749,8824,8900,9045", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "247,2875,2965,3072,3145,5588,5747,5806,5864,5942,5999,6055,6114,6172,6226,6312,6368,6426,6480,6545,6638,6712,6790,6880,6943,7006,7083,7150,7216,7280,7349,7424,7485,7556,7623,7683,7763,7826,7909,7994,8068,8133,8209,8257,8321,8397,8475,8537,8601,8664,8744,8819,8895,8971,9109"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,440,540,622,719,827,904,979,1071,1165,1256,1352,1447,1541,1637,1729,1821,1913,1991,2087,2182,2277,2374,2470,2568,2719,9233", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "342,435,535,617,714,822,899,974,1066,1160,1251,1347,1442,1536,1632,1724,1816,1908,1986,2082,2177,2272,2369,2465,2563,2714,2808,9307"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,321,440,608,687", "endColumns": "65,80,68,118,167,78,75", "endOffsets": "166,247,316,435,603,682,758"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5465,5593,8976,9114,9413,9581,9660", "endColumns": "65,80,68,118,167,78,75", "endOffsets": "5526,5669,9040,9228,9576,9655,9731"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "249,348,508,621,721,887,1001,1129,1247,1416,1515,1675,1788,1954,2088,2169,2252", "endColumns": "98,159,112,99,165,113,127,117,168,98,159,112,165,133,80,82,96", "endOffsets": "347,507,620,720,886,1000,1128,1246,1415,1514,1674,1787,1953,2087,2168,2251,2348"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3150,3253,3413,3530,3634,3800,3918,4050,4339,4508,4611,4771,4888,5054,5192,5277,5364", "endColumns": "102,159,116,103,165,117,131,121,168,102,159,116,165,137,84,86,100", "endOffsets": "3248,3408,3525,3629,3795,3913,4045,4167,4503,4606,4766,4883,5049,5187,5272,5359,5460"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "251", "endColumns": "166", "endOffsets": "417"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4172", "endColumns": "166", "endOffsets": "4334"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9312", "endColumns": "100", "endOffsets": "9408"}}]}]}