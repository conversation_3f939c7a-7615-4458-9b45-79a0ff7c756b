{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-in/values-in.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,340,476,645,730", "endColumns": "68,86,78,135,168,84,78", "endOffsets": "169,256,335,471,640,725,804"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6052,6186,10035,10194,10516,10685,10770", "endColumns": "68,86,78,135,168,84,78", "endOffsets": "6116,6268,10109,10325,10680,10765,10844"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,389,493,598,685,789,905,988,1066,1157,1250,1345,1439,1539,1632,1727,1821,1912,2003,2089,2192,2297,2398,2502,2611,2719,2879,10330", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "384,488,593,680,784,900,983,1061,1152,1245,1340,1434,1534,1627,1722,1816,1907,1998,2084,2187,2292,2393,2497,2606,2714,2874,2973,10410"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-in/values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10415", "endColumns": "100", "endOffsets": "10511"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-in/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,350,546,670,774,988,1113,1254,1385,1610,1713,1908,2032,2246,2416,2506,2592", "endColumns": "104,195,123,103,213,124,140,130,224,102,194,123,213,169,89,85,103", "endOffsets": "349,545,669,773,987,1112,1253,1384,1609,1712,1907,2031,2245,2415,2505,2591,2695"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3362,3471,3667,3795,3903,4117,4246,4391,4717,4942,5049,5244,5372,5586,5760,5854,5944", "endColumns": "108,195,127,107,213,128,144,134,224,106,194,127,213,173,93,89,107", "endOffsets": "3466,3662,3790,3898,4112,4241,4386,4521,4937,5044,5239,5367,5581,5755,5849,5939,6047"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-in/values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,303,409,525,608,673,767,832,891,978,1040,1100,1166,1228,1282,1394,1451,1512,1566,1638,1764,1850,1934,2043,2124,2205,2295,2362,2428,2500,2584,2667,2742,2818,2891,2966,3051,3126,3218,3312,3386,3459,3553,3605,3674,3759,3846,3908,3972,4035,4138,4238,4333,4435", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "219,298,404,520,603,668,762,827,886,973,1035,1095,1161,1223,1277,1389,1446,1507,1561,1633,1759,1845,1929,2038,2119,2200,2290,2357,2423,2495,2579,2662,2737,2813,2886,2961,3046,3121,3213,3307,3381,3454,3548,3600,3669,3754,3841,3903,3967,4030,4133,4233,4328,4430,4510"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2978,3057,3163,3279,6121,6273,6367,6432,6491,6578,6640,6700,6766,6828,6882,6994,7051,7112,7166,7238,7364,7450,7534,7643,7724,7805,7895,7962,8028,8100,8184,8267,8342,8418,8491,8566,8651,8726,8818,8912,8986,9059,9153,9205,9274,9359,9446,9508,9572,9635,9738,9838,9933,10114", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "269,3052,3158,3274,3357,6181,6362,6427,6486,6573,6635,6695,6761,6823,6877,6989,7046,7107,7161,7233,7359,7445,7529,7638,7719,7800,7890,7957,8023,8095,8179,8262,8337,8413,8486,8561,8646,8721,8813,8907,8981,9054,9148,9200,9269,9354,9441,9503,9567,9630,9733,9833,9928,10030,10189"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-in/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "190", "endOffsets": "437"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4526", "endColumns": "190", "endOffsets": "4712"}}]}]}