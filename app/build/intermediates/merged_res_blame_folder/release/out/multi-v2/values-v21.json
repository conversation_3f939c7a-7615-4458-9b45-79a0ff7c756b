{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-v21/values-v21.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,25,28,31,34,37,40,41,44,49,60,66,72,78,84,90,91,92,93,97,100,103,106,109,113,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,290,378,466,554,641,728,815,902,995,1102,1207,1326,1539,1798,2069,2287,2519,2755,3005,3236,3352,3522,3843,4872,5329,5671,6015,6365,6715,6853,6997,7153,7546,7764,7986,8212,8428,8669,8928", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,18,21,24,27,30,33,36,39,40,43,48,59,65,71,77,83,89,90,91,92,96,99,102,105,108,112,116,119", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "118,197,285,373,461,549,636,723,810,897,990,1097,1202,1321,1534,1793,2064,2282,2514,2750,3000,3231,3347,3517,3838,4867,5324,5666,6010,6360,6710,6848,6992,7148,7541,7759,7981,8207,8423,8664,8923,9100"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,77,78,79,80,82,85,88,183,186,189,192,198,265,266,269,274,285,340,346,352,358,364,365,366,367,371,374,377,380,391,395,399", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,241,320,408,496,584,672,759,846,933,6647,6740,6847,6952,7174,7387,7646,13827,14045,14277,14513,14962,19749,19865,20035,20356,21385,24878,25170,25464,25764,26064,26202,26346,26502,26895,27113,27335,27561,28301,28542,28801", "endLines": "3,4,5,6,7,8,9,10,11,12,77,78,79,80,84,87,90,185,188,191,194,200,265,268,273,284,290,345,351,357,363,364,365,366,370,373,376,379,382,394,398,401", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "236,315,403,491,579,667,754,841,928,1015,6735,6842,6947,7066,7382,7641,7912,14040,14272,14508,14758,15188,19860,20030,20351,21380,21837,25165,25459,25759,26059,26197,26341,26497,26890,27108,27330,27556,27772,28537,28796,28973"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,7", "startColumns": "4,4,4", "startOffsets": "55,120,352", "endLines": "2,6,9", "endColumns": "64,12,12", "endOffsets": "115,347,503"}, "to": {"startLines": "16,329,333", "startColumns": "4,4,4", "startOffsets": "1215,23994,24226", "endLines": "16,332,335", "endColumns": "64,12,12", "endOffsets": "1275,24221,24377"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8499,8684,11670,11867,12066,12189,12312,12425,12608,12863,13064,13153,13264,13497,13598,13693,13816,13945,14062,14239,14338,14473,14616,14751,14870,15071,15190,15283,15394,15450,15557,15752,15863,15996,16091,16182,16273,16366,16483,16622,16693,16776,17456,17513,17571,18265", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8494,8679,11665,11862,12061,12184,12307,12420,12603,12858,13059,13148,13259,13492,13593,13688,13811,13940,14057,14234,14333,14468,14611,14746,14865,15066,15185,15278,15389,15445,15552,15747,15858,15991,16086,16177,16268,16361,16478,16617,16688,16771,17451,17508,17566,18260,18966"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,32,34,35,36,37,39,41,42,43,44,45,47,49,51,53,55,57,58,63,65,67,68,69,71,73,74,75,76,81,91,134,137,180,195,201,203,205,207,210,214,217,218,219,222,223,224,225,226,227,230,231,233,235,237,239,243,245,246,247,248,250,254,256,258,259,260,261,262,263,291,292,293,303,304,305,317", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1280,1371,1474,1577,1682,1789,1898,2007,2116,2225,2334,2441,2544,2663,2818,2973,3078,3199,3300,3447,3588,3691,3810,3917,4020,4175,4346,4495,4660,4817,4968,5087,5438,5587,5736,5848,5995,6148,6295,6370,6459,6546,7071,7917,10675,10860,13630,14763,15193,15316,15439,15552,15735,15990,16191,16280,16391,16624,16725,16820,16943,17072,17189,17366,17465,17600,17743,17878,17997,18198,18317,18410,18521,18577,18684,18879,18990,19123,19218,19309,19400,19493,19610,21842,21913,21996,22619,22676,22734,23358", "endLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,31,33,34,35,36,38,40,41,42,43,44,46,48,50,52,54,56,57,62,64,66,67,68,70,72,73,74,75,76,81,133,136,179,182,197,202,204,206,209,213,216,217,218,221,222,223,224,225,226,229,230,232,234,236,238,242,244,245,246,247,249,253,255,257,258,259,260,261,262,264,291,292,302,303,304,316,328", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1366,1469,1572,1677,1784,1893,2002,2111,2220,2329,2436,2539,2658,2813,2968,3073,3194,3295,3442,3583,3686,3805,3912,4015,4170,4341,4490,4655,4812,4963,5082,5433,5582,5731,5843,5990,6143,6290,6365,6454,6541,6642,7169,10670,10855,13625,13822,14957,15311,15434,15547,15730,15985,16186,16275,16386,16619,16720,16815,16938,17067,17184,17361,17460,17595,17738,17873,17992,18193,18312,18405,18516,18572,18679,18874,18985,19118,19213,19304,19395,19488,19605,19744,21908,21991,22614,22671,22729,23353,23989"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,13,14,15,336,337,338,339,383,386", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1020,1084,1151,24382,24498,24624,24750,27777,27949", "endLines": "2,13,14,15,336,337,338,339,385,390", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1079,1146,1210,24493,24619,24745,24873,27944,28296"}}]}]}