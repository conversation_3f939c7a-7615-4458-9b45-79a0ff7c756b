{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-ja/values-ja.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-ja/values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,277,362,466,542,605,689,753,811,892,956,1011,1070,1127,1181,1274,1330,1387,1441,1507,1607,1683,1764,1856,1918,1980,2059,2126,2192,2262,2332,2409,2473,2544,2612,2675,2754,2817,2897,2979,3051,3122,3194,3242,3306,3381,3458,3520,3584,3647,3733,3817,3898,3983", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "203,272,357,461,537,600,684,748,806,887,951,1006,1065,1122,1176,1269,1325,1382,1436,1502,1602,1678,1759,1851,1913,1975,2054,2121,2187,2257,2327,2404,2468,2539,2607,2670,2749,2812,2892,2974,3046,3117,3189,3237,3301,3376,3453,3515,3579,3642,3728,3812,3893,3978,4051"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2844,2913,2998,3102,5693,5839,5923,5987,6045,6126,6190,6245,6304,6361,6415,6508,6564,6621,6675,6741,6841,6917,6998,7090,7152,7214,7293,7360,7426,7496,7566,7643,7707,7778,7846,7909,7988,8051,8131,8213,8285,8356,8428,8476,8540,8615,8692,8754,8818,8881,8967,9051,9132,9286", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "253,2908,2993,3097,3173,5751,5918,5982,6040,6121,6185,6240,6299,6356,6410,6503,6559,6616,6670,6736,6836,6912,6993,7085,7147,7209,7288,7355,7421,7491,7561,7638,7702,7773,7841,7904,7983,8046,8126,8208,8280,8351,8423,8471,8535,8610,8687,8749,8813,8876,8962,9046,9127,9212,9354"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-ja/values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,324,452,620,700", "endColumns": "66,82,68,127,167,79,75", "endOffsets": "167,250,319,447,615,695,771"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5626,5756,9217,9359,9667,9835,9915", "endColumns": "66,82,68,127,167,79,75", "endOffsets": "5688,5834,9281,9482,9830,9910,9986"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-ja/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,347,513,631,735,926,1044,1175,1299,1492,1591,1755,1872,2051,2197,2283,2368", "endColumns": "101,165,117,103,190,117,130,123,192,98,163,116,178,145,85,84,95", "endOffsets": "346,512,630,734,925,1043,1174,1298,1491,1590,1754,1871,2050,2196,2282,2367,2463"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3178,3284,3450,3572,3680,3871,3993,4128,4437,4630,4733,4897,5018,5197,5347,5437,5526", "endColumns": "105,165,121,107,190,121,134,127,192,102,163,120,178,149,89,88,99", "endOffsets": "3279,3445,3567,3675,3866,3988,4123,4251,4625,4728,4892,5013,5192,5342,5432,5521,5621"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-ja/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "180", "endOffsets": "427"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4256", "endColumns": "180", "endOffsets": "4432"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-ja/values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "258,355,448,553,635,733,841,919,994,1085,1178,1273,1367,1467,1560,1655,1749,1840,1931,2009,2111,2209,2304,2407,2503,2599,2747,9487", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "350,443,548,630,728,836,914,989,1080,1173,1268,1362,1462,1555,1650,1744,1835,1926,2004,2106,2204,2299,2402,2498,2594,2742,2839,9561"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-ja/values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9566", "endColumns": "100", "endOffsets": "9662"}}]}]}