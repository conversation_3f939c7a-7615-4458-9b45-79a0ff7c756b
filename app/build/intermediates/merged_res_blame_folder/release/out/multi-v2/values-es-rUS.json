{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10714", "endColumns": "100", "endOffsets": "10810"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,491,660,748", "endColumns": "69,96,76,141,168,87,81", "endOffsets": "170,267,344,486,655,743,825"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6214,6349,10329,10489,10815,10984,11072", "endColumns": "69,96,76,141,168,87,81", "endOffsets": "6279,6441,10401,10626,10979,11067,11149"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-es-rUS/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "249,355,553,683,789,1009,1138,1287,1418,1648,1755,1956,2087,2323,2527,2623,2711", "endColumns": "105,197,129,105,219,128,148,130,229,106,200,130,235,203,95,87,102", "endOffsets": "354,552,682,788,1008,1137,1286,1417,1647,1754,1955,2086,2322,2526,2622,2710,2813"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3396,3506,3704,3838,3948,4168,4301,4454,4794,5024,5135,5336,5471,5707,5915,6015,6107", "endColumns": "109,197,133,109,219,132,152,134,229,110,200,134,235,207,99,91,106", "endOffsets": "3501,3699,3833,3943,4163,4296,4449,4584,5019,5130,5331,5466,5702,5910,6010,6102,6209"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-es-rUS/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "251", "endColumns": "204", "endOffsets": "455"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4589", "endColumns": "204", "endOffsets": "4789"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1346,1464,1522,1584,1641,1721,1855,1944,2025,2136,2217,2297,2387,2454,2520,2596,2678,2766,2839,2916,2986,3063,3152,3226,3320,3422,3494,3575,3679,3732,3799,3892,3981,4043,4107,4170,4281,4378,4480,4578", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,56,117,57,61,56,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1341,1459,1517,1579,1636,1716,1850,1939,2020,2131,2212,2292,2382,2449,2515,2591,2673,2761,2834,2911,2981,3058,3147,3221,3315,3417,3489,3570,3674,3727,3794,3887,3976,4038,4102,4165,4276,4373,4475,4573,4656"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2999,3085,3187,3315,6284,6446,6541,6611,6674,6767,6839,6902,6976,7040,7097,7215,7273,7335,7392,7472,7606,7695,7776,7887,7968,8048,8138,8205,8271,8347,8429,8517,8590,8667,8737,8814,8903,8977,9071,9173,9245,9326,9430,9483,9550,9643,9732,9794,9858,9921,10032,10129,10231,10406", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,56,117,57,61,56,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "278,3080,3182,3310,3391,6344,6536,6606,6669,6762,6834,6897,6971,7035,7092,7210,7268,7330,7387,7467,7601,7690,7771,7882,7963,8043,8133,8200,8266,8342,8424,8512,8585,8662,8732,8809,8898,8972,9066,9168,9240,9321,9425,9478,9545,9638,9727,9789,9853,9916,10027,10124,10226,10324,10484"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,403,512,620,705,807,923,1008,1088,1179,1272,1367,1461,1560,1653,1752,1848,1939,2030,2112,2219,2318,2417,2525,2633,2740,2899,10631", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "398,507,615,700,802,918,1003,1083,1174,1267,1362,1456,1555,1648,1747,1843,1934,2025,2107,2214,2313,2412,2520,2628,2735,2894,2994,10709"}}]}]}