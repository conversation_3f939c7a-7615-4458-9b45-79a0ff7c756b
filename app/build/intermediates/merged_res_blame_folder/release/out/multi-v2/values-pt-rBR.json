{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,413,533,614,678,770,849,909,999,1070,1133,1208,1272,1326,1453,1511,1573,1627,1706,1847,1934,2016,2125,2208,2292,2379,2446,2512,2586,2666,2753,2826,2903,2972,3046,3134,3211,3304,3400,3474,3554,3651,3703,3769,3856,3944,4006,4070,4133,4245,4354,4461,4571", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "223,307,408,528,609,673,765,844,904,994,1065,1128,1203,1267,1321,1448,1506,1568,1622,1701,1842,1929,2011,2120,2203,2287,2374,2441,2507,2581,2661,2748,2821,2898,2967,3041,3129,3206,3299,3395,3469,3549,3646,3698,3764,3851,3939,4001,4065,4128,4240,4349,4456,4566,4643"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3016,3100,3201,3321,6223,6374,6466,6545,6605,6695,6766,6829,6904,6968,7022,7149,7207,7269,7323,7402,7543,7630,7712,7821,7904,7988,8075,8142,8208,8282,8362,8449,8522,8599,8668,8742,8830,8907,9000,9096,9170,9250,9347,9399,9465,9552,9640,9702,9766,9829,9941,10050,10157,10345", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,83,100,119,80,63,91,78,59,89,70,62,74,63,53,126,57,61,53,78,140,86,81,108,82,83,86,66,65,73,79,86,72,76,68,73,87,76,92,95,73,79,96,51,65,86,87,61,63,62,111,108,106,109,76", "endOffsets": "273,3095,3196,3316,3397,6282,6461,6540,6600,6690,6761,6824,6899,6963,7017,7144,7202,7264,7318,7397,7538,7625,7707,7816,7899,7983,8070,8137,8203,8277,8357,8444,8517,8594,8663,8737,8825,8902,8995,9091,9165,9245,9342,9394,9460,9547,9635,9697,9761,9824,9936,10045,10152,10262,10417"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,398,504,611,700,801,920,1005,1085,1176,1269,1364,1458,1558,1651,1746,1841,1932,2023,2108,2215,2326,2428,2536,2644,2754,2916,10573", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "393,499,606,695,796,915,1000,1080,1171,1264,1359,1453,1553,1646,1741,1836,1927,2018,2103,2210,2321,2423,2531,2639,2749,2911,3011,10654"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,491,660,747", "endColumns": "69,86,77,150,168,86,80", "endOffsets": "170,257,335,486,655,742,823"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6153,6287,10267,10422,10760,10929,11016", "endColumns": "69,86,77,150,168,86,80", "endOffsets": "6218,6369,10340,10568,10924,11011,11092"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "251", "endColumns": "203", "endOffsets": "454"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4572", "endColumns": "203", "endOffsets": "4771"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-pt-rBR/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "249,352,532,657,763,989,1117,1265,1395,1624,1730,1925,2053,2269,2452,2547,2639", "endColumns": "102,179,124,105,225,127,147,129,228,105,194,127,215,182,94,91,108", "endOffsets": "351,531,656,762,988,1116,1264,1394,1623,1729,1924,2052,2268,2451,2546,2638,2747"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3402,3509,3689,3818,3928,4154,4286,4438,4776,5005,5115,5310,5442,5658,5845,5944,6040", "endColumns": "106,179,128,109,225,131,151,133,228,109,194,131,215,186,98,95,112", "endOffsets": "3504,3684,3813,3923,4149,4281,4433,4567,5000,5110,5305,5437,5653,5840,5939,6035,6148"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-pt-rBR/values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10659", "endColumns": "100", "endOffsets": "10755"}}]}]}