{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-hu/values-hu.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,482,651,730", "endColumns": "70,87,80,136,168,78,75", "endOffsets": "171,259,340,477,646,725,801"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6318,6453,10381,10547,10869,11038,11117", "endColumns": "70,87,80,136,168,78,75", "endOffsets": "6384,6536,10457,10679,11033,11112,11188"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "268,376,468,583,667,782,905,982,1057,1148,1241,1336,1430,1530,1623,1718,1813,1904,1995,2078,2188,2298,2398,2509,2618,2737,2919,10684", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "371,463,578,662,777,900,977,1052,1143,1236,1331,1425,1525,1618,1713,1808,1899,1990,2073,2183,2293,2393,2504,2613,2732,2914,3017,10763"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-hu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,354,570,706,813,1040,1176,1324,1459,1688,1794,2010,2145,2376,2574,2671,2765", "endColumns": "108,215,135,106,226,135,147,134,228,105,215,134,230,197,96,93,112", "endOffsets": "353,569,705,812,1039,1175,1323,1458,1687,1793,2009,2144,2375,2573,2670,2764,2877"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3401,3514,3730,3870,3981,4208,4348,4500,4875,5104,5214,5430,5569,5800,6002,6103,6201", "endColumns": "112,215,139,110,226,139,151,138,228,109,215,138,230,201,100,97,116", "endOffsets": "3509,3725,3865,3976,4203,4343,4495,4634,5099,5209,5425,5564,5795,5997,6098,6196,6313"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-hu/values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10768", "endColumns": "100", "endOffsets": "10864"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-hu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "235", "endOffsets": "482"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4639", "endColumns": "235", "endOffsets": "4870"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,295,394,514,597,661,760,835,894,1004,1073,1131,1203,1264,1319,1422,1479,1539,1594,1675,1795,1878,1966,2071,2154,2234,2328,2395,2461,2537,2619,2705,2782,2857,2936,3013,3109,3186,3278,3375,3449,3534,3631,3683,3750,3838,3925,3987,4051,4114,4212,4309,4403,4501", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "213,290,389,509,592,656,755,830,889,999,1068,1126,1198,1259,1314,1417,1474,1534,1589,1670,1790,1873,1961,2066,2149,2229,2323,2390,2456,2532,2614,2700,2777,2852,2931,3008,3104,3181,3273,3370,3444,3529,3626,3678,3745,3833,3920,3982,4046,4109,4207,4304,4398,4496,4581"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3022,3099,3198,3318,6389,6541,6640,6715,6774,6884,6953,7011,7083,7144,7199,7302,7359,7419,7474,7555,7675,7758,7846,7951,8034,8114,8208,8275,8341,8417,8499,8585,8662,8737,8816,8893,8989,9066,9158,9255,9329,9414,9511,9563,9630,9718,9805,9867,9931,9994,10092,10189,10283,10462", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,76,98,119,82,63,98,74,58,109,68,57,71,60,54,102,56,59,54,80,119,82,87,104,82,79,93,66,65,75,81,85,76,74,78,76,95,76,91,96,73,84,96,51,66,87,86,61,63,62,97,96,93,97,84", "endOffsets": "263,3094,3193,3313,3396,6448,6635,6710,6769,6879,6948,7006,7078,7139,7194,7297,7354,7414,7469,7550,7670,7753,7841,7946,8029,8109,8203,8270,8336,8412,8494,8580,8657,8732,8811,8888,8984,9061,9153,9250,9324,9409,9506,9558,9625,9713,9800,9862,9926,9989,10087,10184,10278,10376,10542"}}]}]}