{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-ru/values-ru.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,336,420,517,654,746,812,911,988,1051,1169,1234,1291,1361,1422,1476,1592,1649,1711,1765,1839,1967,2055,2141,2248,2332,2417,2508,2575,2641,2713,2791,2887,2967,3043,3120,3197,3286,3359,3449,3544,3618,3699,3792,3847,3913,3999,4084,4146,4210,4273,4371,4471,4566,4668", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "331,415,512,649,741,807,906,983,1046,1164,1229,1286,1356,1417,1471,1587,1644,1706,1760,1834,1962,2050,2136,2243,2327,2412,2503,2570,2636,2708,2786,2882,2962,3038,3115,3192,3281,3354,3444,3539,3613,3694,3787,3842,3908,3994,4079,4141,4205,4268,4366,4466,4561,4663,4743"}, "to": {"startLines": "2,35,36,37,38,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3103,3187,3284,3421,6349,6507,6606,6683,6746,6864,6929,6986,7056,7117,7171,7287,7344,7406,7460,7534,7662,7750,7836,7943,8027,8112,8203,8270,8336,8408,8486,8582,8662,8738,8815,8892,8981,9054,9144,9239,9313,9394,9487,9542,9608,9694,9779,9841,9905,9968,10066,10166,10261,10439", "endLines": "7,35,36,37,38,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,109", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "381,3182,3279,3416,3508,6410,6601,6678,6741,6859,6924,6981,7051,7112,7166,7282,7339,7401,7455,7529,7657,7745,7831,7938,8022,8107,8198,8265,8331,8403,8481,8577,8657,8733,8810,8887,8976,9049,9139,9234,9308,9389,9482,9537,9603,9689,9774,9836,9900,9963,10061,10161,10256,10358,10514"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-ru/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "215", "endOffsets": "462"}, "to": {"startLines": "47", "startColumns": "4", "startOffsets": "4670", "endColumns": "215", "endOffsets": "4881"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-ru/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,350,548,672,780,981,1108,1249,1378,1618,1723,1917,2041,2267,2451,2546,2632", "endColumns": "104,197,123,107,200,126,140,128,239,104,193,123,225,183,94,85,110", "endOffsets": "349,547,671,779,980,1107,1248,1377,1617,1722,1916,2040,2266,2450,2545,2631,2742"}, "to": {"startLines": "39,40,41,42,43,44,45,46,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3513,3622,3820,3948,4060,4261,4392,4537,4886,5126,5235,5429,5557,5783,5971,6070,6160", "endColumns": "108,197,127,111,200,130,144,132,239,108,193,127,225,187,98,89,114", "endOffsets": "3617,3815,3943,4055,4256,4387,4532,4665,5121,5230,5424,5552,5778,5966,6065,6155,6270"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,347,492,661,743", "endColumns": "73,91,75,144,168,81,77", "endOffsets": "174,266,342,487,656,738,816"}, "to": {"startLines": "57,59,108,110,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6275,6415,10363,10519,10847,11016,11098", "endColumns": "73,91,75,144,168,81,77", "endOffsets": "6344,6502,10434,10659,11011,11093,11171"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-ru/values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "386,501,603,702,788,893,1014,1093,1169,1261,1355,1450,1543,1638,1732,1828,1923,2015,2107,2196,2302,2409,2507,2616,2723,2837,3003,10664", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "496,598,697,783,888,1009,1088,1164,1256,1350,1445,1538,1633,1727,1823,1918,2010,2102,2191,2297,2404,2502,2611,2718,2832,2998,3098,10741"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-ru/values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "10746", "endColumns": "100", "endOffsets": "10842"}}]}]}