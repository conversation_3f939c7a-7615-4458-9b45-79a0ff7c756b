{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,492,599,688,789,907,992,1072,1164,1258,1355,1449,1548,1642,1738,1833,1925,2017,2102,2209,2320,2422,2530,2638,2745,2910,10652", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "381,487,594,683,784,902,987,1067,1159,1253,1350,1444,1543,1637,1733,1828,1920,2012,2097,2204,2315,2417,2525,2633,2740,2905,3004,10733"}}, {"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "251", "endColumns": "203", "endOffsets": "454"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4584", "endColumns": "203", "endOffsets": "4783"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "10738", "endColumns": "100", "endOffsets": "10834"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "249,352,547,673,779,1006,1132,1280,1414,1661,1767,1968,2097,2315,2519,2613,2708", "endColumns": "102,194,125,105,226,125,147,133,246,105,200,128,217,203,93,94,111", "endOffsets": "351,546,672,778,1005,1131,1279,1413,1660,1766,1967,2096,2314,2518,2612,2707,2819"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3395,3502,3697,3827,3937,4164,4294,4446,4788,5035,5145,5346,5479,5697,5905,6003,6102", "endColumns": "106,194,129,109,226,129,151,137,246,109,200,132,217,207,97,98,115", "endOffsets": "3497,3692,3822,3932,4159,4289,4441,4579,5030,5140,5341,5474,5692,5900,5998,6097,6213"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,310,413,533,614,678,770,849,914,1004,1072,1134,1207,1271,1325,1451,1509,1571,1625,1701,1844,1931,2013,2122,2204,2286,2373,2440,2506,2581,2661,2748,2821,2898,2971,3045,3138,3215,3308,3406,3480,3561,3660,3713,3779,3868,3956,4018,4082,4145,4261,4364,4471,4575", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "223,305,408,528,609,673,765,844,909,999,1067,1129,1202,1266,1320,1446,1504,1566,1620,1696,1839,1926,2008,2117,2199,2281,2368,2435,2501,2576,2656,2743,2816,2893,2966,3040,3133,3210,3303,3401,3475,3556,3655,3708,3774,3863,3951,4013,4077,4140,4256,4359,4466,4570,4656"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3009,3091,3194,3314,6288,6440,6532,6611,6676,6766,6834,6896,6969,7033,7087,7213,7271,7333,7387,7463,7606,7693,7775,7884,7966,8048,8135,8202,8268,8343,8423,8510,8583,8660,8733,8807,8900,8977,9070,9168,9242,9323,9422,9475,9541,9630,9718,9780,9844,9907,10023,10126,10233,10415", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "273,3086,3189,3309,3390,6347,6527,6606,6671,6761,6829,6891,6964,7028,7082,7208,7266,7328,7382,7458,7601,7688,7770,7879,7961,8043,8130,8197,8263,8338,8418,8505,8578,8655,8728,8802,8895,8972,9065,9163,9237,9318,9417,9470,9536,9625,9713,9775,9839,9902,10018,10121,10228,10332,10496"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,341,492,661,748", "endColumns": "69,87,77,150,168,86,80", "endOffsets": "170,258,336,487,656,743,824"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6218,6352,10337,10501,10839,11008,11095", "endColumns": "69,87,77,150,168,86,80", "endOffsets": "6283,6435,10410,10647,11003,11090,11171"}}]}]}