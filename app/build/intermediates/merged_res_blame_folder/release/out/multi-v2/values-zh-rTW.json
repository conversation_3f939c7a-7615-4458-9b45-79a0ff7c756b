{"logs": [{"outputFile": "com.lockscreen.app-mergeReleaseResources-3:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "/root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/res/values-zh-rTW/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "251", "endColumns": "161", "endOffsets": "412"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4181", "endColumns": "161", "endOffsets": "4338"}}, {"source": "/root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,321,441,609,688", "endColumns": "65,80,68,119,167,78,75", "endOffsets": "166,247,316,436,604,683,759"}, "to": {"startLines": "55,57,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5467,5595,8986,9124,9424,9592,9671", "endColumns": "65,80,68,119,167,78,75", "endOffsets": "5528,5671,9050,9239,9587,9666,9742"}}, {"source": "/root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9323", "endColumns": "100", "endOffsets": "9419"}}, {"source": "/root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,363,470,543,605,683,743,803,881,939,995,1055,1113,1167,1252,1308,1366,1420,1485,1577,1651,1728,1818,1881,1944,2021,2088,2154,2217,2285,2363,2424,2495,2562,2624,2703,2768,2851,2936,3010,3074,3150,3198,3262,3338,3416,3478,3542,3605,3685,3762,3838,3915", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "197,264,358,465,538,600,678,738,798,876,934,990,1050,1108,1162,1247,1303,1361,1415,1480,1572,1646,1723,1813,1876,1939,2016,2083,2149,2212,2280,2358,2419,2490,2557,2619,2698,2763,2846,2931,3005,3069,3145,3193,3257,3333,3411,3473,3537,3600,3680,3757,3833,3910,3979"}, "to": {"startLines": "2,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2818,2885,2979,3086,5533,5676,5754,5814,5874,5952,6010,6066,6126,6184,6238,6323,6379,6437,6491,6556,6648,6722,6799,6889,6952,7015,7092,7159,7225,7288,7356,7434,7495,7566,7633,7695,7774,7839,7922,8007,8081,8145,8221,8269,8333,8409,8487,8549,8613,8676,8756,8833,8909,9055", "endLines": "5,33,34,35,36,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,107", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "247,2880,2974,3081,3154,5590,5749,5809,5869,5947,6005,6061,6121,6179,6233,6318,6374,6432,6486,6551,6643,6717,6794,6884,6947,7010,7087,7154,7220,7283,7351,7429,7490,7561,7628,7690,7769,7834,7917,8002,8076,8140,8216,8264,8328,8404,8482,8544,8608,8671,8751,8828,8904,8981,9119"}}, {"source": "/root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/res/values-zh-rTW/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "249,348,508,621,721,887,1001,1127,1247,1416,1515,1675,1788,1949,2083,2165,2248", "endColumns": "98,159,112,99,165,113,125,119,168,98,159,112,160,133,81,82,98", "endOffsets": "347,507,620,720,886,1000,1126,1246,1415,1514,1674,1787,1948,2082,2164,2247,2346"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3159,3262,3422,3539,3643,3809,3927,4057,4343,4512,4615,4775,4892,5053,5191,5277,5364", "endColumns": "102,159,116,103,165,117,129,123,168,102,159,116,160,137,85,86,102", "endOffsets": "3257,3417,3534,3638,3804,3922,4052,4176,4507,4610,4770,4887,5048,5186,5272,5359,5462"}}, {"source": "/root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,440,540,622,719,827,904,979,1071,1165,1262,1358,1453,1547,1643,1735,1827,1919,1997,2093,2188,2283,2380,2476,2574,2724,9244", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "342,435,535,617,714,822,899,974,1066,1160,1257,1353,1448,1542,1638,1730,1822,1914,1992,2088,2183,2278,2375,2471,2569,2719,2813,9318"}}]}]}