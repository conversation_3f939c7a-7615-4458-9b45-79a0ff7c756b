-- Merging decision tree log ---
manifest
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
MERGED from [com.google.android.material:material:1.4.0] /root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] /root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.1.1] /root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.3.0] /root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-location:18.0.0] /root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /root/.gradle/caches/transforms-3/0b52811f51de9a0dcbbefeb2f6298972/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] /root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] /root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.3.4] /root/.gradle/caches/transforms-3/c119dff1bd73c5f3292699d4f585d352/transformed/fragment-1.3.4/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.3] /root/.gradle/caches/transforms-3/2acd013e9e3066b2c6d9363d50389286/transformed/jetified-activity-1.2.3/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.3.0] /root/.gradle/caches/transforms-3/3c2a851d967e92986ffead089daaf9a1/transformed/jetified-appcompat-resources-1.3.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /root/.gradle/caches/transforms-3/e89f067bfa86e4a18d078e9c4406d0ea/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /root/.gradle/caches/transforms-3/9dc63fb162b011a8c708a21bc0d79bd4/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /root/.gradle/caches/transforms-3/65a844cc5dd39a5eb70353a038d57f2f/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /root/.gradle/caches/transforms-3/170b8a2cb72f855a00f5c71bebfe83bf/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /root/.gradle/caches/transforms-3/6c376253eb6e129894cb7c65233af65d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /root/.gradle/caches/transforms-3/663879c1f16d1f2371d9c3017510e1d9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/b268d2f769a8867aa56bdc1b2aed6d1c/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /root/.gradle/caches/transforms-3/9f696acb8d2e8dca6b1be9ed5088a295/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/65b33b9091dda4ea3e48406dacf435a8/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /root/.gradle/caches/transforms-3/bedc0ebe5eb899b1eb26399ed2312b70/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/e3d5d9db1d8847be9a6ab85c036cf2a5/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /root/.gradle/caches/transforms-3/39585d7ff596c3f6c695fa13a4bfa70a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] /root/.gradle/caches/transforms-3/b9abe9dc14f11c07c7b65d00dcfb4b29/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] /root/.gradle/caches/transforms-3/bae0b629c4a2b1864a7e704a80e3f04a/transformed/jetified-savedstate-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:17:1-32:12
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] /root/.gradle/caches/transforms-3/2980610ab32c7c4a636688c9b7a85a97/transformed/jetified-lifecycle-service-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /root/.gradle/caches/transforms-3/b7836514ac2d7634e9557935af5a3d44/transformed/lifecycle-runtime-2.3.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] /root/.gradle/caches/transforms-3/468a5b0895a944c02bd90f36de604649/transformed/lifecycle-viewmodel-2.3.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] /root/.gradle/caches/transforms-3/918aaabc1355d10d38c7a3fd8d9d1ca9/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] /root/.gradle/caches/transforms-3/48fb1aacf8914590bd292c92bc1a142b/transformed/lifecycle-livedata-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] /root/.gradle/caches/transforms-3/fd2aa8afb289030042fb0f20628a70ff/transformed/lifecycle-livedata-core-2.3.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] /root/.gradle/caches/transforms-3/f2b4b7bd9e585b355e40553c3b3c443b/transformed/core-runtime-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/cb8d1266c5d00603bdadd8ca862b0019/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /root/.gradle/caches/transforms-3/f2dea4af95c493a893bccf4ef1f4f6f0/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /root/.gradle/caches/transforms-3/984ec6733cf30892d757d05b4951f391/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /root/.gradle/caches/transforms-3/583e60085bf9160870b0d0972848fafd/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /root/.gradle/caches/transforms-3/431e5abf6be8c49ace162b4d492e1b23/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.0.0] /root/.gradle/caches/transforms-3/a0583fe1e38e86f99f02f37e6672f112/transformed/jetified-annotation-experimental-1.0.0/AndroidManifest.xml:17:1-24:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
	package
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:3:5-33
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:1-65:12
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:6:5-81
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:6:22-78
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:7:5-75
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:7:22-72
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:8:5-78
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:8:22-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:9:5-80
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.CAMERA
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:11:5-65
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:11:22-62
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:12:5-79
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:12:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:13:5-81
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:13:22-78
uses-permission#android.permission.INTERNET
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:14:5-67
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:14:22-64
application
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:16:5-63:19
MERGED from [com.google.android.material:material:1.4.0] /root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0] /root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] /root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] /root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-location:18.0.0] /root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:18.0.0] /root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] /root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] /root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] /root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] /root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:24:5-30:19
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:24:5-30:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:19:9-35
	android:label
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:18:9-41
	android:allowBackup
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:17:9-35
	android:theme
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:20:9-40
activity#com.lockscreen.app.activities.MainActivity
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:25:13-36
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:24:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:27:17-69
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:28:17-77
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:28:27-74
activity#com.lockscreen.app.activities.SettingsActivity
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:33:9-36:69
	android:parentActivityName
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:36:13-66
	android:label
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:35:13-51
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:34:13-56
activity#com.lockscreen.app.activities.LockScreenActivity
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:39:9-45:54
	android:screenOrientation
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:43:13-49
	android:excludeFromRecents
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:41:13-46
	android:launchMode
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:42:13-48
	android:showOnLockScreen
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:44:13-44
	android:theme
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:45:13-51
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:40:13-58
service#com.lockscreen.app.services.LockScreenService
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:48:9-51:40
	android:enabled
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:50:13-35
	android:exported
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:51:13-37
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:49:13-55
receiver#com.lockscreen.app.receivers.BootCompletedReceiver
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:54:9-61:20
	android:enabled
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:56:13-35
	android:exported
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:57:13-36
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:55:13-60
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:58:13-60:29
action#android.intent.action.BOOT_COMPLETED
ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:59:17-79
	android:name
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml:59:25-76
uses-sdk
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
MERGED from [com.google.android.material:material:1.4.0] /root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0] /root/.gradle/caches/transforms-3/49fcea26672a241f7537d4d09551b5b4/transformed/material-1.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] /root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] /root/.gradle/caches/transforms-3/cb2d491d977356c1337a6e184ac2bc43/transformed/constraintlayout-2.0.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.1.1] /root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.1.1] /root/.gradle/caches/transforms-3/c28253b35f51f2bc877c3079a96dd2e3/transformed/preference-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.3.0] /root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.3.0] /root/.gradle/caches/transforms-3/489a4fad7ae900edda14a7a705460884/transformed/appcompat-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /root/.gradle/caches/transforms-3/4e805d111083769b7c9d316d1ecf8db1/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:18.0.0] /root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:18.0.0] /root/.gradle/caches/transforms-3/4edf2339ba043786862f987796f23a70/transformed/jetified-play-services-location-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /root/.gradle/caches/transforms-3/0b52811f51de9a0dcbbefeb2f6298972/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /root/.gradle/caches/transforms-3/0b52811f51de9a0dcbbefeb2f6298972/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] /root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] /root/.gradle/caches/transforms-3/d28b3ec676c898f6610872a82bf5a922/transformed/jetified-play-services-places-placereport-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] /root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] /root/.gradle/caches/transforms-3/6292d6f882caa66fc52acb069a5a010a/transformed/jetified-play-services-tasks-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.4] /root/.gradle/caches/transforms-3/c119dff1bd73c5f3292699d4f585d352/transformed/fragment-1.3.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.4] /root/.gradle/caches/transforms-3/c119dff1bd73c5f3292699d4f585d352/transformed/fragment-1.3.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.3] /root/.gradle/caches/transforms-3/2acd013e9e3066b2c6d9363d50389286/transformed/jetified-activity-1.2.3/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.3] /root/.gradle/caches/transforms-3/2acd013e9e3066b2c6d9363d50389286/transformed/jetified-activity-1.2.3/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.0] /root/.gradle/caches/transforms-3/3c2a851d967e92986ffead089daaf9a1/transformed/jetified-appcompat-resources-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.0] /root/.gradle/caches/transforms-3/3c2a851d967e92986ffead089daaf9a1/transformed/jetified-appcompat-resources-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /root/.gradle/caches/transforms-3/e89f067bfa86e4a18d078e9c4406d0ea/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /root/.gradle/caches/transforms-3/e89f067bfa86e4a18d078e9c4406d0ea/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /root/.gradle/caches/transforms-3/9dc63fb162b011a8c708a21bc0d79bd4/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /root/.gradle/caches/transforms-3/9dc63fb162b011a8c708a21bc0d79bd4/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /root/.gradle/caches/transforms-3/65a844cc5dd39a5eb70353a038d57f2f/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /root/.gradle/caches/transforms-3/65a844cc5dd39a5eb70353a038d57f2f/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /root/.gradle/caches/transforms-3/170b8a2cb72f855a00f5c71bebfe83bf/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /root/.gradle/caches/transforms-3/170b8a2cb72f855a00f5c71bebfe83bf/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /root/.gradle/caches/transforms-3/6c376253eb6e129894cb7c65233af65d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /root/.gradle/caches/transforms-3/6c376253eb6e129894cb7c65233af65d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /root/.gradle/caches/transforms-3/663879c1f16d1f2371d9c3017510e1d9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /root/.gradle/caches/transforms-3/663879c1f16d1f2371d9c3017510e1d9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/b268d2f769a8867aa56bdc1b2aed6d1c/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /root/.gradle/caches/transforms-3/b268d2f769a8867aa56bdc1b2aed6d1c/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /root/.gradle/caches/transforms-3/9f696acb8d2e8dca6b1be9ed5088a295/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /root/.gradle/caches/transforms-3/9f696acb8d2e8dca6b1be9ed5088a295/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/65b33b9091dda4ea3e48406dacf435a8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /root/.gradle/caches/transforms-3/65b33b9091dda4ea3e48406dacf435a8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /root/.gradle/caches/transforms-3/bedc0ebe5eb899b1eb26399ed2312b70/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /root/.gradle/caches/transforms-3/bedc0ebe5eb899b1eb26399ed2312b70/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/e3d5d9db1d8847be9a6ab85c036cf2a5/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /root/.gradle/caches/transforms-3/e3d5d9db1d8847be9a6ab85c036cf2a5/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.5.0] /root/.gradle/caches/transforms-3/9a250a3e73faef0b9a8652933e13baab/transformed/core-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /root/.gradle/caches/transforms-3/39585d7ff596c3f6c695fa13a4bfa70a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /root/.gradle/caches/transforms-3/39585d7ff596c3f6c695fa13a4bfa70a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] /root/.gradle/caches/transforms-3/b9abe9dc14f11c07c7b65d00dcfb4b29/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] /root/.gradle/caches/transforms-3/b9abe9dc14f11c07c7b65d00dcfb4b29/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] /root/.gradle/caches/transforms-3/bae0b629c4a2b1864a7e704a80e3f04a/transformed/jetified-savedstate-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] /root/.gradle/caches/transforms-3/bae0b629c4a2b1864a7e704a80e3f04a/transformed/jetified-savedstate-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] /root/.gradle/caches/transforms-3/2980610ab32c7c4a636688c9b7a85a97/transformed/jetified-lifecycle-service-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-service:2.2.0] /root/.gradle/caches/transforms-3/2980610ab32c7c4a636688c9b7a85a97/transformed/jetified-lifecycle-service-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /root/.gradle/caches/transforms-3/b7836514ac2d7634e9557935af5a3d44/transformed/lifecycle-runtime-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /root/.gradle/caches/transforms-3/b7836514ac2d7634e9557935af5a3d44/transformed/lifecycle-runtime-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] /root/.gradle/caches/transforms-3/468a5b0895a944c02bd90f36de604649/transformed/lifecycle-viewmodel-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] /root/.gradle/caches/transforms-3/468a5b0895a944c02bd90f36de604649/transformed/lifecycle-viewmodel-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] /root/.gradle/caches/transforms-3/918aaabc1355d10d38c7a3fd8d9d1ca9/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /root/.gradle/caches/transforms-3/918aaabc1355d10d38c7a3fd8d9d1ca9/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] /root/.gradle/caches/transforms-3/48fb1aacf8914590bd292c92bc1a142b/transformed/lifecycle-livedata-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.2.0] /root/.gradle/caches/transforms-3/48fb1aacf8914590bd292c92bc1a142b/transformed/lifecycle-livedata-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] /root/.gradle/caches/transforms-3/fd2aa8afb289030042fb0f20628a70ff/transformed/lifecycle-livedata-core-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] /root/.gradle/caches/transforms-3/fd2aa8afb289030042fb0f20628a70ff/transformed/lifecycle-livedata-core-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /root/.gradle/caches/transforms-3/f2b4b7bd9e585b355e40553c3b3c443b/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /root/.gradle/caches/transforms-3/f2b4b7bd9e585b355e40553c3b3c443b/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /root/.gradle/caches/transforms-3/f28438c8cbca08992bb12d19384568e9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/cb8d1266c5d00603bdadd8ca862b0019/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /root/.gradle/caches/transforms-3/cb8d1266c5d00603bdadd8ca862b0019/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /root/.gradle/caches/transforms-3/f2dea4af95c493a893bccf4ef1f4f6f0/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /root/.gradle/caches/transforms-3/f2dea4af95c493a893bccf4ef1f4f6f0/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /root/.gradle/caches/transforms-3/984ec6733cf30892d757d05b4951f391/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /root/.gradle/caches/transforms-3/984ec6733cf30892d757d05b4951f391/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /root/.gradle/caches/transforms-3/583e60085bf9160870b0d0972848fafd/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /root/.gradle/caches/transforms-3/583e60085bf9160870b0d0972848fafd/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /root/.gradle/caches/transforms-3/431e5abf6be8c49ace162b4d492e1b23/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /root/.gradle/caches/transforms-3/431e5abf6be8c49ace162b4d492e1b23/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.0.0] /root/.gradle/caches/transforms-3/a0583fe1e38e86f99f02f37e6672f112/transformed/jetified-annotation-experimental-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] /root/.gradle/caches/transforms-3/a0583fe1e38e86f99f02f37e6672f112/transformed/jetified-annotation-experimental-1.0.0/AndroidManifest.xml:20:5-22:41
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/LockScreenApp/app/src/main/AndroidManifest.xml
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:23:9-26:75
	android:exported
		ADDED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-base:17.5.0] /root/.gradle/caches/transforms-3/b848832fbe5e366fd564dd50fe716004/transformed/jetified-play-services-base-17.5.0/AndroidManifest.xml:24:13-79
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:17.5.0] /root/.gradle/caches/transforms-3/461a414d6898885270555891bf18fbba/transformed/jetified-play-services-basement-17.5.0/AndroidManifest.xml:24:13-58
provider#androidx.lifecycle.ProcessLifecycleOwnerInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:25:9-29:43
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:27:13-69
	android:multiprocess
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:29:13-40
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.2.0] /root/.gradle/caches/transforms-3/1d9a08468cb0baff909fb7951fbb2603/transformed/jetified-lifecycle-process-2.2.0/AndroidManifest.xml:26:13-79
