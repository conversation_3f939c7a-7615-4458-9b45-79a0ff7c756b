androidx.versionedparcelable.CustomVersionedParcelable
androidx.lifecycle.ProcessLifecycleOwnerInitializer
androidx.lifecycle.SingleGeneratedAdapterObserver
androidx.appcompat.view.menu.ActionMenuItemView
com.lockscreen.app.activities.LockScreenActivity
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader
androidx.preference.PreferenceCategory
androidx.preference.UnPressableLinearLayout
com.google.android.material.transformation.FabTransformationBehavior
androidx.lifecycle.FullLifecycleObserverAdapter
com.google.android.gms.common.api.internal.LifecycleCallback
com.google.android.material.datepicker.MaterialCalendarGridView
androidx.preference.MultiSelectListPreference
androidx.preference.DialogPreference
com.google.android.material.internal.NavigationMenuItemView
androidx.appcompat.widget.Toolbar
androidx.fragment.app.DialogFragment
androidx.preference.PreferenceDialogFragmentCompat
androidx.preference.SwitchPreferenceCompat
com.google.android.gms.common.api.internal.BasePendingResult
com.google.android.material.timepicker.TimePickerView
androidx.appcompat.view.menu.ListMenuItemView
androidx.appcompat.widget.ActionBarContextView
androidx.preference.Preference
androidx.appcompat.app.AlertController$RecycleListView
androidx.fragment.app.Fragment$5
androidx.recyclerview.widget.GridLayoutManager
com.google.android.material.transformation.FabTransformationSheetBehavior
com.google.android.gms.common.annotation.KeepName
com.google.android.material.datepicker.MaterialCalendar
androidx.lifecycle.CompositeGeneratedAdaptersObserver
com.google.android.gms.common.GooglePlayServicesManifestException
androidx.savedstate.Recreator
com.google.android.gms.common.GooglePlayServicesIncorrectManifestValueException
androidx.preference.EditTextPreferenceDialogFragmentCompat
androidx.appcompat.widget.ViewStubCompat
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.savedstate.SavedStateRegistry$1
com.google.android.material.chip.ChipGroup
android.support.v4.graphics.drawable.IconCompatParcelizer
com.google.android.material.snackbar.SnackbarContentLayout
com.google.android.material.internal.NavigationMenuView
androidx.appcompat.widget.AlertDialogLayout
com.google.android.material.transformation.ExpandableTransformationBehavior
com.google.android.material.behavior.HideBottomViewOnScrollBehavior
com.google.android.material.appbar.AppBarLayout$BaseBehavior
androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable
com.google.android.material.button.MaterialButton
androidx.preference.PreferenceGroup
androidx.appcompat.widget.SwitchCompat
androidx.preference.ListPreferenceDialogFragmentCompat
androidx.core.widget.NestedScrollView
com.lockscreen.app.services.LockScreenService
com.lockscreen.app.activities.SettingsActivity$SettingsFragment
androidx.annotation.Keep
androidx.appcompat.widget.ContentFrameLayout
androidx.activity.ComponentActivity$5
androidx.transition.FragmentTransitionSupport
androidx.preference.TwoStatePreference
com.lockscreen.app.receivers.BootCompletedReceiver
androidx.activity.result.ActivityResultRegistry$1
androidx.activity.ImmLeaksCleaner
com.google.android.material.snackbar.Snackbar$SnackbarLayout
androidx.activity.ComponentActivity$4
androidx.core.app.RemoteActionCompatParcelizer
com.google.android.material.button.MaterialButtonToggleGroup
com.google.android.gms.common.internal.ReflectedParcelable
com.google.android.material.bottomsheet.BottomSheetBehavior
androidx.preference.EditTextPreference
androidx.preference.DropDownPreference
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior
androidx.preference.SwitchPreference
androidx.constraintlayout.widget.Barrier
androidx.appcompat.widget.DialogTitle
androidx.coordinatorlayout.widget.CoordinatorLayout
com.google.android.gms.common.SupportErrorDialogFragment
com.google.android.gms.auth.api.signin.GoogleSignInAccount
android.support.v4.app.RemoteActionCompatParcelizer
com.google.android.material.theme.MaterialComponentsViewInflater
com.google.android.material.timepicker.ChipTextInputComboView
androidx.appcompat.widget.ButtonBarLayout
com.google.android.material.internal.BaselineLayout
com.google.android.gms.common.api.Status
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.appcompat.widget.SearchView
androidx.appcompat.widget.ActionMenuView
com.google.android.material.transformation.FabTransformationScrimBehavior
com.google.android.material.datepicker.MaterialDatePicker
com.google.android.material.appbar.AppBarLayout$Behavior
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.core.app.CoreComponentFactory
com.google.android.material.textview.MaterialTextView
com.google.android.material.bottomappbar.BottomAppBar$Behavior
androidx.appcompat.view.menu.ExpandedMenuView
androidx.preference.PreferenceScreen
com.lockscreen.app.activities.MainActivity
com.google.android.material.timepicker.ClockHandView
com.google.android.material.transformation.ExpandableBehavior
androidx.lifecycle.ReflectiveGenericLifecycleObserver
androidx.lifecycle.SavedStateHandleController
androidx.appcompat.widget.SearchView$SearchAutoComplete
com.lockscreen.app.views.PatternView
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.core.app.RemoteActionCompat
androidx.core.graphics.drawable.IconCompat
androidx.recyclerview.widget.LinearLayoutManager
androidx.preference.ListPreference
com.google.android.material.behavior.SwipeDismissBehavior
androidx.constraintlayout.widget.ConstraintLayout
androidx.viewpager2.adapter.FragmentStateAdapter$2
com.google.android.gms.common.api.GoogleApiActivity
androidx.preference.SeekBarPreference
androidx.recyclerview.widget.RecyclerView
androidx.appcompat.widget.ActionBarContainer
androidx.lifecycle.Lifecycling$1
androidx.preference.CheckBoxPreference
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior
androidx.preference.PreferenceFragmentCompat
androidx.appcompat.widget.ActionBarOverlayLayout
com.google.android.material.textfield.TextInputLayout
androidx.activity.ComponentActivity$3
com.google.android.material.datepicker.MaterialTextInputPicker
androidx.fragment.app.FragmentManager$6
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior
androidx.viewpager2.adapter.FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3
com.google.android.material.chip.Chip
com.google.android.material.internal.CheckableImageButton
com.google.android.gms.common.util.DynamiteApi
com.google.android.gms.common.api.Scope
androidx.preference.MultiSelectListPreferenceDialogFragmentCompat
com.google.android.material.appbar.MaterialToolbar
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior
androidx.lifecycle.SavedStateHandleController$1
com.google.android.material.textfield.TextInputEditText
com.google.android.material.timepicker.ClockFaceView
androidx.versionedparcelable.ParcelImpl
androidx.lifecycle.LiveData$LifecycleBoundObserver
com.lockscreen.app.activities.SettingsActivity
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior
androidx.viewpager2.adapter.FragmentStateAdapter$5
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.constraintlayout.widget.Guideline
androidx.preference.internal.PreferenceImageView
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.google.android.gms.signin.internal.zaj: android.os.Parcelable$Creator CREATOR
androidx.preference.SeekBarPreference$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.api.internal.BasePendingResult: com.google.android.gms.common.api.internal.BasePendingResult$zaa mResultGuardian
androidx.preference.ListPreference$SavedState: android.os.Parcelable$Creator CREATOR
androidx.preference.TwoStatePreference$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.signin.internal.zak: android.os.Parcelable$Creator CREATOR
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zaaa: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.GetServiceRequest: android.os.Parcelable$Creator CREATOR
com.google.android.material.bottomsheet.BottomSheetBehavior$SavedState: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.ConnectionResult: android.os.Parcelable$Creator CREATOR
androidx.preference.MultiSelectListPreference$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: java.lang.ClassLoader sClassLoader
com.google.android.material.datepicker.Month: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.lifecycle.ProcessLifecycleOwner$3$1: androidx.lifecycle.ProcessLifecycleOwner$3 this$1
com.google.android.gms.common.internal.ConnectionTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
com.google.android.material.datepicker.DateValidatorPointForward: android.os.Parcelable$Creator CREATOR
androidx.transition.ChangeBounds$7: androidx.transition.ChangeBounds$ViewBounds mViewBounds
androidx.coordinatorlayout.widget.CoordinatorLayout$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.Feature: android.os.Parcelable$Creator CREATOR
com.google.android.material.datepicker.CalendarConstraints: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.appbar.AppBarLayout$BaseBehavior$SavedState: android.os.Parcelable$Creator CREATOR
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.AppCompatSpinner$SavedState: android.os.Parcelable$Creator CREATOR
androidx.preference.Preference$BaseSavedState: android.os.Parcelable$Creator CREATOR
androidx.preference.EditTextPreference$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.api.Scope: android.os.Parcelable$Creator CREATOR
androidx.preference.PreferenceGroup$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zao: android.os.Parcelable$Creator CREATOR
com.google.android.gms.auth.api.signin.GoogleSignInAccount: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
com.google.android.gms.signin.internal.zai: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.api.Status: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.google.android.gms.common.internal.RootTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.signin.internal.zaa: android.os.Parcelable$Creator CREATOR
androidx.customview.widget.ExploreByTouchHelper: int mHoveredVirtualViewId
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.google.android.material.timepicker.TimeModel: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
com.google.android.material.bottomappbar.BottomAppBar$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zzc: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zat: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.ProcessLifecycleOwner$3: androidx.lifecycle.ProcessLifecycleOwner this$0
com.google.android.material.textfield.TextInputLayout$SavedState: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zau: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.google.android.material.stateful.ExtendableSavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.internal.CheckableImageButton$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.button.MaterialButton$SavedState: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setTextAppearanceResource(int)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
com.google.android.material.chip.Chip: void setGravity(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconCheckable(boolean)
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
com.google.android.material.behavior.SwipeDismissBehavior: SwipeDismissBehavior()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
com.google.android.material.chip.Chip: void setChipIcon(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
com.google.android.material.textfield.TextInputLayout: int getBaseline()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setShrinkMotionSpecResource(int)
com.google.android.material.button.MaterialButton: void setBackground(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: float getCompatHoveredFocusedTranslationZ()
com.google.android.material.chip.Chip: void setRippleColorResource(int)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
com.google.android.material.card.MaterialCardView: android.content.res.ColorStateList getCheckedIconTint()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior getBehavior()
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.appcompat.app.AppCompatViewInflater: AppCompatViewInflater()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setEndIconCheckable(boolean)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.google.android.material.card.MaterialCardView: void setCardForegroundColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorStateList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setChecked(boolean)
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(boolean)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterOverflowTextColor()
com.google.android.material.bottomappbar.BottomAppBar: float getFabTranslationX()
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
com.google.android.material.card.MaterialCardView: float getRadius()
com.google.android.material.textfield.TextInputEditText: void setTextInputLayoutFocusedRectEnabled(boolean)
com.google.android.material.chip.ChipGroup: void setChipSpacing(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: CoordinatorLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
androidx.cardview.widget.CardView: void setCardElevation(float)
com.google.android.material.chip.ChipGroup: void setChipSpacingVerticalResource(int)
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setShowMotionSpecResource(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconVisible(boolean)
com.google.android.material.button.MaterialButtonToggleGroup: void setSelectionRequired(boolean)
com.google.android.material.snackbar.SnackbarContentLayout: void setMaxInlineActionWidth(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.lockscreen.app.activities.SettingsActivity: SettingsActivity()
androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type[] values()
com.google.android.material.card.MaterialCardView: float getCardViewRadius()
com.google.android.material.chip.Chip: void setMaxWidth(int)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
com.google.android.material.chip.ChipGroup: void setFlexWrap(int)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatToggleButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.ConstraintLayout: void setMaxWidth(int)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.SpinnerAdapter)
com.google.android.material.chip.Chip: void setCheckedIconResource(int)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.ForegroundLinearLayout: void setForeground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
com.google.android.material.internal.NavigationMenuItemView: void setIconTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
com.google.android.gms.common.api.PendingResult: PendingResult()
com.google.android.material.chip.Chip: com.google.android.material.resources.TextAppearance getTextAppearance()
com.google.android.material.appbar.AppBarLayout: void setElevation(float)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setOnHierarchyChangeListener(android.view.ViewGroup$OnHierarchyChangeListener)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
com.google.android.material.bottomappbar.BottomAppBar: void setSubtitle(java.lang.CharSequence)
com.google.android.material.chip.Chip: void setCloseIconEndPadding(float)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.core.graphics.drawable.IconCompat: IconCompat()
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHintFromLayout()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getPrefixTextView()
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: float getHintCollapsedTextHeight()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: EmptyActivityLifecycleCallbacks()
com.google.android.material.card.MaterialCardView: void setCardElevation(float)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatHoveredFocusedTranslationZ(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
com.google.android.material.chip.ChipGroup: void setChipSpacingHorizontal(int)
com.google.android.material.chip.Chip: float getChipEndPadding()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
com.google.android.material.chip.Chip: void setShowMotionSpecResource(int)
com.google.android.material.button.MaterialButton: void setCornerRadiusResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: float getCompatElevation()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.Chip: void setIconEndPadding(float)
com.google.android.material.textfield.TextInputLayout: void setHint(int)
androidx.constraintlayout.widget.ConstraintLayout: void setMinWidth(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setEnsureMinTouchTargetSize(boolean)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportButtonTintList()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
com.google.android.material.chip.Chip: void setChipIconSizeResource(int)
com.google.android.material.circularreveal.cardview.CircularRevealCardView: void setCircularRevealOverlayDrawable(android.graphics.drawable.Drawable)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
com.google.android.material.appbar.HeaderBehavior: HeaderBehavior()
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.recyclerview.widget.RecyclerView: java.lang.CharSequence getAccessibilityClassName()
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.preference.MultiSelectListPreferenceDialogFragmentCompat: MultiSelectListPreferenceDialogFragmentCompat()
com.google.android.material.button.MaterialButton: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButton: int getIconSize()
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setElevation(float)
androidx.cardview.widget.CardView: float getRadius()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnClickListener(android.view.View$OnClickListener)
com.google.android.material.button.MaterialButton: int getIconPadding()
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
com.google.android.material.textfield.TextInputLayout: void setEndIconMode(int)
com.google.android.material.chip.Chip: float getTextStartPadding()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup getInternalPopup()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxHeight()
com.google.android.material.textfield.TextInputLayout: int getMaxWidth()
androidx.appcompat.widget.AppCompatSpinner: void setPrompt(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.circularreveal.CircularRevealFrameLayout: int getCircularRevealScrimColor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setCloseIconContentDescription(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.FloatingActionButton: com.google.android.material.floatingactionbutton.FloatingActionButtonImpl getImpl()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.card.MaterialCardView: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.constraintlayout.widget.Placeholder: int getEmptyVisibility()
com.google.android.material.chip.ChipGroup: java.util.List getCheckedChipIds()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.internal.CheckableImageButton getEndIconToUpdateDummyDrawable()
com.google.android.material.button.MaterialButton: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
com.google.android.material.chip.Chip: void setChipMinHeight(float)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
com.google.android.material.textfield.TextInputLayout: void setPrefixTextAppearance(int)
com.google.android.material.chip.Chip: void setCheckedIconVisible(boolean)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: int getCollapsedSize()
com.google.android.material.chip.Chip: void setChipIconEnabledResource(int)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setShowMotionSpec(com.google.android.material.animation.MotionSpec)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnClickListener(android.view.View$OnClickListener)
com.google.android.material.textfield.TextInputLayout: void setCounterMaxLength(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getHideMotionSpec()
androidx.appcompat.widget.AppCompatSpinner: android.graphics.drawable.Drawable getPopupBackground()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
androidx.appcompat.widget.AppCompatSpinner: android.content.Context getPopupContext()
com.google.android.material.internal.CheckableImageButton: void setPressed(boolean)
com.google.android.material.chip.Chip: void setTextStartPadding(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
com.google.android.material.chip.Chip: void setLayoutDirection(int)
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: boolean getUseCompatPadding()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
com.google.android.material.circularreveal.cardview.CircularRevealCardView: void setCircularRevealScrimColor(int)
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
com.google.android.material.internal.NavigationMenuItemView: void setMaxLines(int)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
com.google.android.material.button.MaterialButton: void setOnPressedChangeListenerInternal(com.google.android.material.button.MaterialButton$OnPressedChangeListener)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setTranslationY(float)
com.google.android.material.bottomappbar.BottomAppBar: void setFabCradleRoundedCornerRadius(float)
com.google.android.material.chip.Chip: void setTextEndPaddingResource(int)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable: AbstractSafeParcelable()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getBoxStrokeErrorColor()
com.google.android.material.bottomappbar.BottomAppBar: com.google.android.material.bottomappbar.BottomAppBarTopEdgeTreatment getTopEdgeTreatment()
androidx.appcompat.widget.AppCompatToggleButton: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setImageResource(int)
com.google.android.material.textfield.TextInputLayout: void setMaxWidth(int)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextColor(android.content.res.ColorStateList)
com.google.android.material.card.MaterialCardView: android.graphics.drawable.Drawable getCheckedIcon()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackground(android.graphics.drawable.Drawable)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.activity.ComponentActivity: ComponentActivity()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatSpinner: java.lang.CharSequence getPrompt()
androidx.core.app.NotificationCompat$BubbleMetadata$Api29Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
com.google.android.material.chip.Chip: void setMaxLines(int)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.card.MaterialCardView: android.content.res.ColorStateList getStrokeColorStateList()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(int)
com.google.android.material.button.MaterialButton: void setStrokeWidth(int)
com.google.android.material.textfield.TextInputLayout: void setErrorTextAppearance(int)
com.google.android.material.internal.NavigationMenuItemView: void setNeedsEmptyIcon(boolean)
com.google.android.material.chip.Chip: void setCloseIconSizeResource(int)
com.google.android.material.textfield.TextInputLayout: void setHintTextAppearance(int)
androidx.cardview.widget.CardView: void setCardBackgroundColor(android.content.res.ColorStateList)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
com.google.android.material.chip.ChipGroup: void setShowDividerHorizontal(int)
com.lockscreen.app.views.PatternView: PatternView(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.google.android.material.button.MaterialButton: void setIconResource(int)
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomStart()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getErrorContentDescription()
androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type: androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type valueOf(java.lang.String)
androidx.preference.EditTextPreferenceDialogFragmentCompat: EditTextPreferenceDialogFragmentCompat()
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.constraintlayout.widget.Barrier: void setMargin(int)
com.google.android.material.textfield.TextInputLayout: void setMinWidthResource(int)
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.appcompat.widget.AbsActionBarView: void setContentHeight(int)
androidx.constraintlayout.widget.VirtualLayout: void setElevation(float)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackground(android.graphics.drawable.Drawable)
com.google.android.material.appbar.AppBarLayout: void setOrientation(int)
com.google.android.material.chip.Chip: void setCloseIconEnabledResource(int)
androidx.appcompat.widget.DropDownListView: void setSelectorEnabled(boolean)
com.google.android.material.chip.Chip: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.appbar.HeaderScrollingViewBehavior: HeaderScrollingViewBehavior()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
com.google.android.material.appbar.HeaderScrollingViewBehavior: HeaderScrollingViewBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.card.MaterialCardView: void setClickable(boolean)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: com.google.android.material.animation.MotionSpec getExtendMotionSpec()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPlaceholderTextColor()
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
com.google.android.material.chip.Chip: void setChipCornerRadiusResource(int)
com.google.android.material.textfield.TextInputLayout: void setHintEnabled(boolean)
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButton: void setRippleColorResource(int)
com.google.android.material.timepicker.RadialViewGroup: RadialViewGroup(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: int getTextWidth()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setBackgroundColor(int)
androidx.constraintlayout.widget.Constraints: androidx.constraintlayout.widget.ConstraintSet getConstraintSet()
com.google.android.material.appbar.AppBarLayout: void setTargetElevation(float)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopEnd()
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getErrorIconDrawable()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setHideMotionSpecResource(int)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
com.google.android.material.internal.NavigationMenuView: int getWindowAnimations()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorResource(int)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getEndIconDrawable()
com.google.android.material.textfield.TextInputLayout: void setExpandedHintEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomEnd()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownHorizontalOffset()
com.google.android.material.button.MaterialButton: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.Adapter)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setVisibility(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
com.google.android.material.card.MaterialCardView: void setCheckedIconSize(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCustomSize(int)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
com.lockscreen.app.views.PatternView: void setOnPatternListener(com.lockscreen.app.views.PatternView$OnPatternListener)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipIcon()
androidx.activity.ComponentActivity: void setContentView(android.view.View)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getRippleColor()
com.google.android.material.button.MaterialButton: void setCornerRadius(int)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
com.google.android.material.textfield.TextInputLayout: void setSuffixTextColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setEndIconVisible(boolean)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.chip.ChipGroup: void setDividerDrawableVertical(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setEndIconTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.AppCompatCheckBox: AppCompatCheckBox(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
com.google.android.material.button.MaterialButton: void setIconTintResource(int)
androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColor(int)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.google.android.material.floatingactionbutton.FloatingActionButton: android.graphics.drawable.Drawable getContentBackground()
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector valueOf(java.lang.String)
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
com.google.android.material.chip.Chip: void setChipStrokeWidth(float)
androidx.lifecycle.ViewModel: ViewModel()
com.google.android.material.chip.Chip: void setIconEndPaddingResource(int)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
com.google.android.material.internal.FlowLayout: int getLineSpacing()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidthFocused()
com.google.android.material.floatingactionbutton.FloatingActionButton: android.content.res.ColorStateList getRippleColorStateList()
com.google.android.material.button.MaterialButton: void setIconGravity(int)
com.google.android.material.chip.Chip: void setChipIconSize(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityStopped(android.app.Activity)
com.google.android.material.chip.Chip: void setMinLines(int)
com.google.android.material.chip.Chip: void setTextStartPaddingResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setHideMotionSpec(com.google.android.material.animation.MotionSpec)
com.google.android.material.checkbox.MaterialCheckBox: void setUseMaterialThemeColors(boolean)
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: DynamiteModule$DynamiteLoaderClassLoader()
androidx.coordinatorlayout.widget.CoordinatorLayout: android.graphics.drawable.Drawable getStatusBarBackground()
com.google.android.material.circularreveal.CircularRevealFrameLayout: void setCircularRevealOverlayDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownWidth()
androidx.recyclerview.widget.RecyclerView$LayoutManager: RecyclerView$LayoutManager()
com.google.android.material.textfield.TextInputLayout: TextInputLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatElevationResource(int)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
com.google.android.material.chip.Chip: float getTextEndPadding()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getSuffixText()
com.google.android.material.card.MaterialCardView: void setStrokeWidth(int)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getPasswordVisibilityToggleDrawable()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnAttachStateChangeListener(com.google.android.material.snackbar.BaseTransientBottomBar$OnAttachStateChangeListener)
com.google.android.material.appbar.AppBarLayout: void setStatusBarForegroundColor(int)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.cardview.widget.CardView: void setMaxCardElevation(float)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
com.google.android.material.chip.Chip: void setChipStrokeColorResource(int)
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior()
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
com.google.android.material.button.MaterialButton: void setStrokeColorResource(int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatCheckBox: int getCompoundPaddingLeft()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
com.google.android.material.chip.Chip: void setChipIconTintResource(int)
com.google.android.material.card.MaterialCardView: int getStrokeWidth()
androidx.constraintlayout.widget.Guideline: void setVisibility(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
com.google.android.material.chip.Chip: void setElevation(float)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
com.google.android.material.chip.Chip: void setChipIconVisible(boolean)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.FloatingActionButton: int getSize()
com.google.android.material.chip.ChipGroup: int getChipSpacingHorizontal()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.cardview.widget.CardView: void setMinimumWidth(int)
com.google.android.material.appbar.HeaderBehavior: HeaderBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.appbar.MaterialToolbar: void setTitleCentered(boolean)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.constraintlayout.widget.Guideline: Guideline(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.google.android.material.internal.FlowLayout: void setItemSpacing(int)
com.google.android.material.textfield.TextInputLayout: void setHintInternal(java.lang.CharSequence)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getIconTintMode()
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
com.google.android.material.card.MaterialCardView: void setCardBackgroundColor(int)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.shape.MaterialShapeDrawable getBoxBackground()
com.google.android.material.floatingactionbutton.FloatingActionButton: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.preference.ListPreferenceDialogFragmentCompat: ListPreferenceDialogFragmentCompat()
com.google.android.material.bottomappbar.BottomAppBar: int getRightInset()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
com.google.android.material.timepicker.ClockHandView: ClockHandView(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderText(java.lang.CharSequence)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
com.google.android.material.chip.Chip: float getChipIconSize()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
com.google.android.material.chip.Chip: void setBackgroundResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextAppearance(int)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
com.google.android.material.bottomappbar.BottomAppBar: float getCradleVerticalOffset()
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.ConstraintLayout: int getMinHeight()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownVerticalOffset()
com.google.android.material.textfield.TextInputLayout: void setTextInputAccessibilityDelegate(com.google.android.material.textfield.TextInputLayout$AccessibilityDelegate)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.ProcessLifecycleOwner$3: void onActivityStopped(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
com.google.android.material.chip.Chip: void setChipStrokeColor(android.content.res.ColorStateList)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: com.google.android.material.animation.MotionSpec getHideMotionSpec()
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.card.MaterialCardView: void setCheckedIconTint(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setCheckedIconEnabledResource(int)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton: android.graphics.PorterDuff$Mode getBackgroundTintMode()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
com.google.android.material.internal.NavigationMenuItemView: void setIconSize(int)
androidx.lifecycle.SavedStateHandleController$OnRecreation: SavedStateHandleController$OnRecreation()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setUseCompatPadding(boolean)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setExtendMotionSpec(com.google.android.material.animation.MotionSpec)
com.google.android.material.textfield.TextInputLayout: int getMinWidth()
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(int)
com.google.android.material.internal.CheckableImageButton: void setPressable(boolean)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getSuffixTextColor()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
com.google.android.material.button.MaterialButtonToggleGroup: void setupButtonChild(com.google.android.material.button.MaterialButton)
com.google.android.material.textfield.TextInputLayout: void setCounterEnabled(boolean)
com.google.android.material.chip.ChipGroup: void setChipSpacingResource(int)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownHorizontalOffset(int)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.textfield.EndIconDelegate getEndIconDelegate()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.ProcessLifecycleOwner$3$1: void onActivityPostStarted(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.constraintlayout.widget.Placeholder: void setEmptyVisibility(int)
com.google.android.material.chip.Chip: void setCloseIconSize(float)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.lifecycle.ProcessLifecycleOwner$3: ProcessLifecycleOwner$3(androidx.lifecycle.ProcessLifecycleOwner)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getAnimationMode()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setScaleY(float)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.textfield.TextInputLayout: void setCounterTextColor(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButtonToggleGroup: void setGeneratedIdIfNeeded(com.google.android.material.button.MaterialButton)
com.google.android.material.chip.Chip: void setLines(int)
com.google.android.material.chip.Chip: void setHideMotionSpecResource(int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.cardview.widget.CardView: void setRadius(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
com.lockscreen.app.activities.LockScreenActivity: LockScreenActivity()
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
com.google.android.material.textfield.TextInputLayout: int getErrorCurrentTextColors()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.ChipGroup: int getChipSpacingVertical()
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(java.lang.CharSequence)
androidx.constraintlayout.widget.Placeholder: android.view.View getContent()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setErrorEnabled(boolean)
androidx.cardview.widget.CardView: float getMaxCardElevation()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
com.google.android.material.chip.Chip: float getChipCornerRadius()
androidx.fragment.app.FragmentActivity: FragmentActivity()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundResource(int)
com.google.android.material.textfield.TextInputLayout: int getHelperTextCurrentTextColor()
androidx.lifecycle.ProcessLifecycleOwner$3$1: void onActivityPostResumed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.google.android.material.chip.Chip: float getChipMinHeight()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumWidth()
com.google.android.material.floatingactionbutton.FloatingActionButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.checkbox.MaterialCheckBox: android.content.res.ColorStateList getMaterialThemeColorsTintList()
com.google.android.material.button.MaterialButton: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
com.google.android.material.theme.MaterialComponentsViewInflater: MaterialComponentsViewInflater()
com.google.android.material.chip.Chip: void setCloseIconTintResource(int)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
com.google.android.material.bottomappbar.BottomAppBar: int getFabAnimationMode()
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHint()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setRippleColor(int)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
com.google.android.material.card.MaterialCardView: int getContentPaddingLeft()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
com.google.android.material.chip.Chip: void setCloseIconVisible(boolean)
com.google.android.material.button.MaterialButtonToggleGroup: void setCheckedId(int)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
com.google.android.material.chip.Chip: void setCloseIconStartPaddingResource(int)
com.google.android.material.button.MaterialButtonToggleGroup: java.lang.CharSequence getAccessibilityClassName()
com.google.android.material.chip.ChipGroup: void setOnHierarchyChangeListener(android.view.ViewGroup$OnHierarchyChangeListener)
com.google.android.material.button.MaterialButton: void setBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setCheckedIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.constraintlayout.widget.ConstraintHelper: void setIds(java.lang.String)
com.google.android.material.textfield.TextInputEditText: com.google.android.material.textfield.TextInputLayout getTextInputLayout()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.google.android.material.circularreveal.CircularRevealFrameLayout: android.graphics.drawable.Drawable getCircularRevealOverlayDrawable()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
com.google.android.material.textfield.TextInputLayout: int getEndIconMode()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.circularreveal.cardview.CircularRevealCardView: android.graphics.drawable.Drawable getCircularRevealOverlayDrawable()
com.google.android.material.floatingactionbutton.FloatingActionButton: com.google.android.material.animation.MotionSpec getHideMotionSpec()
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
com.google.android.material.chip.Chip: void setOnCheckedChangeListenerInternal(android.widget.CompoundButton$OnCheckedChangeListener)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getRippleColor()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setDropDownBackgroundResource(int)
com.google.android.material.chip.ChipGroup: void setChipSpacingHorizontalResource(int)
androidx.constraintlayout.widget.ConstraintLayout: ConstraintLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setErrorIconVisible(boolean)
com.google.android.material.internal.FlowLayout: int getRowCount()
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.TextView getMessageView()
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.cardview.widget.CardView: int getContentPaddingRight()
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.ChipGroup: void setSingleSelection(int)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundResource(int)
com.google.android.material.card.MaterialCardView: void setUseCompatPadding(boolean)
com.google.android.material.button.MaterialButton: int getTextHeight()
androidx.constraintlayout.widget.ConstraintLayout: void setOptimizationLevel(int)
com.google.android.material.card.MaterialCardView: int getCheckedIconMargin()
androidx.cardview.widget.CardView: boolean getPreventCornerOverlap()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior()
com.google.android.material.button.MaterialButton: int getIconGravity()
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setTranslationZ(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException: GooglePlayServicesMissingManifestValueException()
com.google.android.material.internal.NavigationMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
com.google.android.material.datepicker.MaterialDatePicker: MaterialDatePicker()
com.google.android.material.internal.ForegroundLinearLayout: android.graphics.drawable.Drawable getForeground()
com.google.android.material.chip.Chip: void setChipBackgroundColorResource(int)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintList(android.content.res.ColorStateList)
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior()
androidx.recyclerview.widget.RecyclerView: boolean isLayoutSuppressed()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
com.google.android.material.bottomappbar.BottomAppBar: float getFabCradleMargin()
com.google.android.material.textfield.TextInputLayout: int getCounterMaxLength()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(int)
androidx.appcompat.widget.AppCompatRadioButton: int getCompoundPaddingLeft()
androidx.core.widget.NestedScrollView: int getScrollRange()
com.google.android.material.card.MaterialCardView: int getCheckedIconSize()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
com.google.android.material.chip.Chip: void setCheckable(boolean)
androidx.coordinatorlayout.widget.CoordinatorLayout: java.util.List getDependencySortedChildren()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.preference.PreferenceFragmentCompat: PreferenceFragmentCompat()
com.google.android.material.chip.Chip: void setChipCornerRadius(float)
com.google.android.material.circularreveal.CircularRevealFrameLayout: void setCircularRevealScrimColor(int)
com.google.android.material.chip.Chip: void setCheckedIconEnabled(boolean)
com.google.android.material.timepicker.TimePickerView: TimePickerView(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setPressed(boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setSize(int)
com.google.android.material.card.MaterialCardView: void setStrokeColor(int)
com.google.android.material.internal.NavigationMenuItemView: void setIconPadding(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundColor(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumHeight()
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior()
androidx.constraintlayout.widget.ConstraintHelper: void setReferencedIds(int[])
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior()
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
com.google.android.material.appbar.MaterialToolbar: void setNavigationIconTint(int)
com.google.android.material.chip.Chip: void setChipEndPadding(float)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocusedResource(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatElevation(float)
com.google.android.material.internal.BaselineLayout: int getBaseline()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColorStateList(android.content.res.ColorStateList)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipDrawable()
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleEnabled(boolean)
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getShowMotionSpec()
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior()
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
com.google.android.material.bottomappbar.BottomAppBar: void setElevation(float)
com.google.android.material.chip.Chip: void setTextAppearance(int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setShowMotionSpecResource(int)
com.google.android.material.chip.Chip: void setCloseIconResource(int)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
com.google.android.material.chip.Chip: void setChipTextResource(int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(int)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
com.google.android.material.button.MaterialButtonToggleGroup: int getLastVisibleChildIndex()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
com.google.android.material.textfield.TextInputLayout: int getPlaceholderTextAppearance()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior: CoordinatorLayout$Behavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.preference.PreferenceDialogFragmentCompat: PreferenceDialogFragmentCompat()
com.lockscreen.app.services.LockScreenService: LockScreenService()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setBackgroundResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: int getRippleColor()
androidx.cardview.widget.CardView: int getContentPaddingBottom()
com.google.android.material.textfield.TextInputLayout: void setStartIconTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.floatingactionbutton.FloatingActionButton: int getSizeDimension()
com.google.android.material.chip.ChipGroup: void setShowDividerVertical(int)
com.google.android.material.chip.Chip: void setCloseIconEndPaddingResource(int)
com.google.android.material.chip.Chip: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.constraintlayout.widget.Guideline: void setGuidelineBegin(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.core.app.NotificationCompat$BubbleMetadata$Api30Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
com.google.android.material.textfield.TextInputLayout: void setStartIconOnLongClickListener(android.view.View$OnLongClickListener)
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.recyclerview.widget.RecyclerView: int getBaseline()
com.google.android.material.card.MaterialCardView: void setProgress(float)
com.google.android.material.chip.Chip: void setSingleLine(boolean)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType[] values()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterTextColor()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
com.google.android.material.circularreveal.cardview.CircularRevealCardView: void setRevealInfo(com.google.android.material.circularreveal.CircularRevealWidget$RevealInfo)
com.google.android.material.button.MaterialButton: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
com.google.android.material.chip.Chip: void setCheckedIconTintResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setScaleX(float)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.textfield.TextInputLayout: android.graphics.Typeface getTypeface()
com.google.android.material.floatingactionbutton.FloatingActionButton: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.chip.ChipGroup: int getCheckedChipId()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
com.google.android.material.appbar.AppBarLayout: int getTopInset()
com.google.android.material.bottomappbar.BottomAppBar: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AbsActionBarView: int getAnimatedVisibility()
com.google.android.material.chip.Chip: void setChipMinHeightResource(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
com.google.android.material.chip.Chip: void setCloseIconPressed(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.constraintlayout.widget.ConstraintLayout: int getOptimizationLevel()
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
com.google.android.material.chip.Chip: float getCloseIconSize()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getHintTextColor()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.material.internal.NavigationMenuItemView: void setTextAppearance(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
com.google.android.material.chip.Chip: void setBackgroundColor(int)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setHideMotionSpec(com.google.android.material.animation.MotionSpec)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
com.google.android.material.textfield.TextInputLayout: void setEnabled(boolean)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
com.google.android.material.appbar.AppBarLayout: void setStatusBarForeground(android.graphics.drawable.Drawable)
com.google.android.material.card.MaterialCardView: int getContentPaddingBottom()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeColor()
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setIconStartPaddingResource(int)
com.google.android.material.bottomappbar.BottomAppBar: float getFabCradleRoundedCornerRadius()
com.google.android.material.textfield.TextInputEditText: TextInputEditText(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setCloseIconTint(android.content.res.ColorStateList)
com.google.android.material.bottomappbar.BottomAppBar: void setBackgroundTint(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColor(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(java.lang.CharSequence)
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.VisibilityAwareImageButton: void setVisibility(int)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPrefixTextColor()
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
com.google.android.material.internal.NavigationMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatHoveredFocusedTranslationZResource(int)
com.google.android.material.button.MaterialButton: void setInsetBottom(int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.lockscreen.app.receivers.BootCompletedReceiver: BootCompletedReceiver()
com.google.android.material.chip.Chip: void setIconStartPadding(float)
com.google.android.material.textfield.TextInputLayout: void setSuffixText(java.lang.CharSequence)
com.google.android.material.radiobutton.MaterialRadioButton: void setUseMaterialThemeColors(boolean)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: int getInputType()
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: int getCollapsedPadding()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
com.google.android.gms.common.api.internal.BasePendingResult: BasePendingResult()
androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour[] values()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatPressedTranslationZResource(int)
com.google.android.material.card.MaterialCardView: android.content.res.ColorStateList getCardBackgroundColor()
com.google.android.material.floatingactionbutton.FloatingActionButton: androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior getBehavior()
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getBackgroundTintList()
com.google.android.material.button.MaterialButton: void setChecked(boolean)
com.google.android.material.textfield.TextInputLayout: void setEndIconTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: float getCloseIconStartPadding()
com.google.android.material.button.MaterialButton: void setStrokeWidthResource(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getCounterOverflowDescription()
com.google.android.material.textfield.TextInputLayout: void setEndIconOnLongClickListener(android.view.View$OnLongClickListener)
androidx.coordinatorlayout.widget.CoordinatorLayout: androidx.core.view.WindowInsetsCompat getLastWindowInsets()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
com.google.android.material.chip.Chip: void setCloseIconVisible(int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: com.google.android.material.animation.MotionSpec getShrinkMotionSpec()
androidx.appcompat.widget.Toolbar: void setLogo(int)
com.google.android.material.card.MaterialCardView: void setBackgroundInternal(android.graphics.drawable.Drawable)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.Button getActionView()
com.google.android.material.internal.NavigationMenuView: NavigationMenuView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType[] values()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: com.google.android.material.animation.MotionSpec getShowMotionSpec()
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.internal.NavigationMenuItemView: void setTitle(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setExtended(boolean)
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context,android.util.AttributeSet,int)
androidx.constraintlayout.solver.SolverVariable$Type: androidx.constraintlayout.solver.SolverVariable$Type valueOf(java.lang.String)
com.google.android.material.chip.Chip: android.graphics.Rect getCloseIconTouchBoundsInt()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setShrinkMotionSpec(com.google.android.material.animation.MotionSpec)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.fragment.app.DialogFragment: DialogFragment()
com.google.android.material.floatingactionbutton.FloatingActionButton: int getCustomSize()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCloseIconTint()
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
com.google.android.material.chip.Chip: void setCloseIconEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
com.google.android.material.internal.FlowLayout: void setLineSpacing(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
com.google.android.material.textfield.TextInputLayout: void setHint(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
com.google.android.material.textfield.TextInputLayout: void setTypeface(android.graphics.Typeface)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getBackgroundTintMode()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHelperText()
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundMode()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthResource(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.constraintlayout.widget.ConstraintHelper: ConstraintHelper(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
com.google.android.material.chip.Chip: void setCloseIcon(android.graphics.drawable.Drawable)
com.google.android.material.appbar.AppBarLayout: float getTargetElevation()
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.cardview.widget.CardView: float getCardElevation()
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: float getChipStrokeWidth()
com.google.android.material.button.MaterialButton: int getStrokeWidth()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
com.google.android.material.button.MaterialButton: int getInsetTop()
com.google.android.material.bottomappbar.BottomAppBar: boolean getHideOnScroll()
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
com.google.android.material.button.MaterialButtonToggleGroup: java.util.List getCheckedButtonIds()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.google.android.material.textfield.TextInputLayout: void setEndIconActivated(boolean)
com.google.android.gms.common.SupportErrorDialogFragment: SupportErrorDialogFragment()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setPrefixTextColor(android.content.res.ColorStateList)
com.google.android.material.card.MaterialCardView: void setCheckedIconMarginResource(int)
com.google.android.material.chip.Chip: void setCloseIconStartPadding(float)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
com.google.android.material.chip.Chip: void setShowMotionSpec(com.google.android.material.animation.MotionSpec)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
com.google.android.material.button.MaterialButton: void setShouldDrawSurfaceColorStroke(boolean)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getBackgroundOverlayColorAlpha()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
com.google.android.material.chip.Chip: void setChipText(java.lang.CharSequence)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setChipDrawable(com.google.android.material.chip.ChipDrawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundColor()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setShadowPaddingEnabled(boolean)
com.google.android.material.circularreveal.cardview.CircularRevealCardView: int getCircularRevealScrimColor()
com.google.android.material.card.MaterialCardView: void setMaxCardElevation(float)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.DropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
com.google.android.material.chip.Chip: void setOnCloseIconClickListener(android.view.View$OnClickListener)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.google.android.material.chip.Chip: float getIconStartPadding()
com.google.android.material.card.MaterialCardView: void setStrokeColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.Chip: void setEllipsize(android.text.TextUtils$TruncateAt)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
com.google.android.material.chip.Chip: void setCheckedIconVisible(int)
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(int)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.textfield.TextInputLayout: void setEditText(android.widget.EditText)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatButton: AppCompatButton(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
com.google.android.material.datepicker.MaterialTextInputPicker: MaterialTextInputPicker()
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
com.google.android.material.appbar.MaterialToolbar: MaterialToolbar(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButtonToggleGroup: int getVisibleButtonCount()
com.google.android.material.button.MaterialButton: int getCornerRadius()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.constraintlayout.widget.ConstraintHelper: void setReferenceTags(java.lang.String)
com.google.android.material.button.MaterialButton: void setCheckable(boolean)
com.google.android.gms.common.api.GoogleApiActivity: GoogleApiActivity()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidth(int)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setExtendMotionSpecResource(int)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextAppearance(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.constraintlayout.widget.Barrier: int getMargin()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setShowMotionSpec(com.google.android.material.animation.MotionSpec)
com.google.android.material.appbar.AppBarLayout: int getMinimumHeightForVisibleOverlappingContent()
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
com.google.android.material.button.MaterialButton: void setInsetTop(int)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setVisibility(int)
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.textfield.TextInputLayout: void setHintTextColor(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCheckedIconTint()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidth()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeErrorColor(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
com.google.android.material.card.MaterialCardView: void setDragged(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
com.google.android.material.textfield.TextInputLayout: void setHelperTextColor(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setChipIconTint(android.content.res.ColorStateList)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.cardview.widget.CardView: void setMinimumHeight(int)
com.google.android.material.appbar.MaterialToolbar: void setElevation(float)
com.google.android.material.button.MaterialButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.Chip: float getIconEndPadding()
com.google.android.material.appbar.AppBarLayout: void setStatusBarForegroundResource(int)
com.google.android.material.button.MaterialButton: void setIconTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.card.MaterialCardView: void setCheckable(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
androidx.constraintlayout.solver.SolverVariable$Type: androidx.constraintlayout.solver.SolverVariable$Type[] values()
com.google.android.material.bottomappbar.BottomAppBar: com.google.android.material.bottomappbar.BottomAppBar$Behavior getBehavior()
com.google.android.material.textfield.TextInputLayout: void setMaxWidthResource(int)
com.google.android.material.bottomappbar.BottomAppBar: int getFabAlignmentMode()
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
com.google.android.material.card.MaterialCardView: void setRadius(float)
com.google.android.material.chip.Chip: void setTextAppearance(com.google.android.material.resources.TextAppearance)
com.google.android.material.chip.Chip: java.lang.CharSequence getChipText()
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
com.google.android.material.card.MaterialCardView: void setOnCheckedChangeListener(com.google.android.material.card.MaterialCardView$OnCheckedChangeListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnLongClickListener(android.view.View$OnLongClickListener)
com.google.android.material.datepicker.MaterialCalendar: MaterialCalendar()
androidx.appcompat.widget.AppCompatImageButton: AppCompatImageButton(android.content.Context,android.util.AttributeSet)
com.google.android.material.appbar.AppBarLayout: int getPendingAction()
com.google.android.material.radiobutton.MaterialRadioButton: android.content.res.ColorStateList getMaterialThemeColorsTintList()
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setHideMotionSpecResource(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnLayoutChangeListener(com.google.android.material.snackbar.BaseTransientBottomBar$OnLayoutChangeListener)
com.google.android.material.chip.Chip: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
com.google.android.material.appbar.AppBarLayout: void setExpanded(boolean)
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$3: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setErrorTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
com.google.android.material.card.MaterialCardView: void setBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
com.google.android.material.chip.Chip: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType valueOf(java.lang.String)
com.google.android.material.internal.CheckableImageButton: void setCheckable(boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.lifecycle.ProcessLifecycleOwnerInitializer: ProcessLifecycleOwnerInitializer()
com.google.android.material.card.MaterialCardView: int getContentPaddingRight()
com.google.android.material.floatingactionbutton.FloatingActionButton: android.content.res.ColorStateList getBackgroundTintList()
com.google.android.gms.common.api.internal.LifecycleCallback: com.google.android.gms.common.api.internal.LifecycleFragment getChimeraLifecycleFragmentImpl(com.google.android.gms.common.api.internal.LifecycleActivity)
com.google.android.material.internal.VisibilityAwareImageButton: int getUserSetVisibility()
com.google.android.material.chip.Chip: void setChipStartPadding(float)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
com.google.android.material.bottomappbar.BottomAppBar: androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior getBehavior()
com.google.android.material.internal.NavigationMenuItemView: NavigationMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.ConstraintLayout: void setConstraintSet(androidx.constraintlayout.widget.ConstraintSet)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
com.google.android.material.appbar.AppBarLayout: androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior getBehavior()
com.google.android.material.circularreveal.CircularRevealFrameLayout: void setRevealInfo(com.google.android.material.circularreveal.CircularRevealWidget$RevealInfo)
com.google.android.material.button.MaterialButton: void setInternalBackground(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundResource(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.button.MaterialButton: void setIconPadding(int)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCheckedIcon()
com.google.android.material.button.MaterialButton: java.lang.String getA11yClassName()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxWidth()
androidx.constraintlayout.widget.ConstraintLayout: void setMaxHeight(int)
com.google.android.material.textfield.TextInputLayout: void setCounterTextAppearance(int)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setCompatPressedTranslationZ(float)
androidx.cardview.widget.CardView: android.content.res.ColorStateList getCardBackgroundColor()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHint()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackground(android.graphics.drawable.Drawable)
com.google.android.material.floatingactionbutton.FloatingActionButton: com.google.android.material.animation.MotionSpec getShowMotionSpec()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipBackgroundColor()
com.google.android.material.chip.Chip: void setEnsureMinTouchTargetSize(boolean)
androidx.constraintlayout.widget.Placeholder: void setContentId(int)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocused(int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getError()
com.google.android.material.button.MaterialButton: void setIconSize(int)
androidx.appcompat.widget.AbsActionBarView: AbsActionBarView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$3: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.ForegroundLinearLayout: void setForegroundGravity(int)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setTextColor(int)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
com.google.android.material.card.MaterialCardView: void setRippleColorResource(int)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.app.AppCompatActivity: void setContentView(android.view.View)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getStartIconDrawable()
com.google.android.material.textfield.TextInputLayout: void setStartIconOnClickListener(android.view.View$OnClickListener)
com.google.android.material.internal.CheckableImageButton: void setChecked(boolean)
com.google.android.material.textfield.TextInputLayout: android.widget.EditText getEditText()
com.google.android.material.textfield.TextInputLayout: void setMinWidth(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.cardview.widget.CardView: int getContentPaddingLeft()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: void setStrokeColor(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButtonToggleGroup: int getCheckedButtonId()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPasswordVisibilityToggleContentDescription()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getIconTint()
com.google.android.material.snackbar.SnackbarContentLayout: SnackbarContentLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ScrollingTabContainerView: void setTabSelected(int)
androidx.cardview.widget.CardView: void setUseCompatPadding(boolean)
com.google.android.material.chip.Chip: void setChipIconEnabled(boolean)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
com.google.android.material.chip.Chip: Chip(android.content.Context,android.util.AttributeSet)
androidx.fragment.app.Fragment: Fragment()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AbsActionBarView: void setVisibility(int)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
com.google.android.material.timepicker.ChipTextInputComboView: ChipTextInputComboView(android.content.Context,android.util.AttributeSet)
com.google.android.material.bottomappbar.BottomAppBar: float getFabTranslationY()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownWidth(int)
com.google.android.material.appbar.MaterialToolbar: void setSubtitleCentered(boolean)
com.google.android.material.chip.Chip: void setChipEndPaddingResource(int)
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(int)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.card.MaterialCardView: void setCheckedIconMargin(int)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.recyclerview.widget.RecyclerView: void suppressLayout(boolean)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownVerticalOffset(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
com.google.android.material.internal.FlowLayout: int getItemSpacing()
com.google.android.material.floatingactionbutton.FloatingActionButton: float getCompatPressedTranslationZ()
androidx.lifecycle.ProcessLifecycleOwner$3$1: ProcessLifecycleOwner$3$1(androidx.lifecycle.ProcessLifecycleOwner$3)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setImageDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintList(android.content.res.ColorStateList)
androidx.appcompat.app.AppCompatActivity: AppCompatActivity()
com.google.android.material.bottomappbar.BottomAppBar: void setHideOnScroll(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
com.google.android.material.button.MaterialButtonToggleGroup: MaterialButtonToggleGroup(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.NavigationMenuItemView: void setCheckable(boolean)
com.lockscreen.app.activities.MainActivity: MainActivity()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
com.google.android.material.chip.ChipGroup: void setSelectionRequired(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton: void setAnimateShowBeforeLayout(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: Snackbar$SnackbarLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.card.MaterialCardView: void setCheckedIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCloseIcon()
com.google.android.material.internal.NavigationMenuItemView: void setActionView(android.view.View)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
com.google.android.material.bottomappbar.BottomAppBar: void setFabAnimationMode(int)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundMode(int)
com.google.android.material.card.MaterialCardView: void setCheckedIconResource(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.constraintlayout.widget.Guideline: void setGuidelineEnd(int)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
com.google.android.material.textfield.MaterialAutoCompleteTextView: java.lang.CharSequence getHint()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
com.google.android.material.button.MaterialButton: void setBackgroundColor(int)
com.google.android.material.chip.Chip: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.floatingactionbutton.FloatingActionButton: int getExpandedComponentIdHint()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
com.google.android.material.card.MaterialCardView: void setPreventCornerOverlap(boolean)
com.google.android.material.textfield.TextInputLayout: void setHintAnimationEnabled(boolean)
com.google.android.material.appbar.ViewOffsetBehavior: ViewOffsetBehavior()
com.google.android.material.chip.Chip: void setHideMotionSpec(com.google.android.material.animation.MotionSpec)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getBackgroundDrawable()
com.google.android.material.circularreveal.CircularRevealFrameLayout: com.google.android.material.circularreveal.CircularRevealWidget$RevealInfo getRevealInfo()
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior: CoordinatorLayout$Behavior()
com.google.android.material.card.MaterialCardView: android.content.res.ColorStateList getCardForegroundColor()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
com.google.android.material.bottomappbar.BottomAppBar: void setCradleVerticalOffset(float)
com.google.android.material.chip.Chip: void setChipStartPaddingResource(int)
androidx.core.app.ComponentActivity: ComponentActivity()
com.google.android.material.textfield.TextInputLayout: int getHintCurrentCollapsedTextColor()
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior: BaseTransientBottomBar$Behavior()
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.constraintlayout.widget.ConstraintLayout: void setId(int)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setTranslationX(float)
androidx.constraintlayout.widget.ConstraintLayout: void setOnConstraintsChanged(androidx.constraintlayout.widget.ConstraintsChangedListener)
com.google.android.material.card.MaterialCardView: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.Chip: void setChipStrokeWidthResource(int)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
com.google.android.material.bottomappbar.BottomAppBar: android.content.res.ColorStateList getBackgroundTint()
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
com.google.android.material.appbar.AppBarLayout: int getDownNestedPreScrollRange()
com.google.android.material.card.MaterialCardView: void setCardBackgroundColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPrefixText()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.constraintlayout.widget.Barrier: void setType(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.Toolbar: void setTitle(int)
com.google.android.material.chip.Chip: void setBackground(android.graphics.drawable.Drawable)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getNestedScrollAxes()
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopStart()
androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type: androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type[] values()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
com.google.android.material.chip.ChipGroup: void setSingleLine(boolean)
com.google.android.material.button.MaterialButton: void setIconTint(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.internal.CheckableImageButton getEndIconView()
androidx.recyclerview.widget.RecyclerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
com.google.android.material.internal.FlowLayout: void setSingleLine(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.google.android.material.textfield.TextInputLayout: void setHelperTextEnabled(boolean)
androidx.constraintlayout.widget.ConstraintLayout: void setMinHeight(int)
com.google.android.material.textfield.TextInputLayout: void setError(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
com.google.android.material.textfield.TextInputLayout: int getErrorTextCurrentColor()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.google.android.material.card.MaterialCardView: void setCheckedIconSizeResource(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
com.google.android.material.appbar.AppBarLayout: int getUpNestedPreScrollRange()
com.google.android.material.chip.Chip: float getCloseIconEndPadding()
com.google.android.material.internal.ForegroundLinearLayout: int getForegroundGravity()
com.google.android.material.card.MaterialCardView: android.content.res.ColorStateList getRippleColor()
com.google.android.material.textfield.TextInputLayout: void setPrefixText(java.lang.CharSequence)
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior()
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
com.google.android.material.chip.Chip: void setCheckableResource(int)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundResource(int)
com.google.android.material.card.MaterialCardView: float getProgress()
com.google.android.material.circularreveal.cardview.CircularRevealCardView: com.google.android.material.circularreveal.CircularRevealWidget$RevealInfo getRevealInfo()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
com.google.android.material.appbar.AppBarLayout: int getTotalScrollRange()
com.google.android.material.appbar.MaterialToolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
com.google.android.material.chip.ChipGroup: void setSingleLine(int)
com.google.android.material.chip.Chip: void setChipBackgroundColor(android.content.res.ColorStateList)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.datepicker.MaterialCalendarGridView: MaterialCalendarGridView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.constraintlayout.widget.ConstraintHelper: int[] getReferencedIds()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setSupportImageTintList(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: int getInsetBottom()
com.google.android.material.chip.ChipGroup: void setOnCheckedChangeListener(com.google.android.material.chip.ChipGroup$OnCheckedChangeListener)
com.google.android.material.chip.Chip: void setChipIconVisible(int)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
com.google.android.material.bottomappbar.BottomAppBar: void setFabAlignmentMode(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
com.google.android.material.internal.NavigationMenuItemView: void setTextColor(android.content.res.ColorStateList)
com.google.android.material.appbar.AppBarLayout: void setLiftOnScrollTargetViewId(int)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
com.google.android.material.card.MaterialCardView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.constraintlayout.widget.Barrier: int getType()
com.google.android.material.chip.ChipGroup: void setChipSpacingVertical(int)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.widget.ScrollingTabContainerView: void setContentHeight(int)
com.google.android.material.chip.ChipGroup: int getChipCount()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getDefaultHintTextColor()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
com.google.android.material.chip.Chip: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getEndIconContentDescription()
com.google.android.material.button.MaterialButtonToggleGroup: int getFirstVisibleChildIndex()
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
com.google.android.material.appbar.AppBarLayout: int getLiftOnScrollTargetViewId()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
com.google.android.material.appbar.AppBarLayout: void setLiftOnScroll(boolean)
com.google.android.material.card.MaterialCardView: int getContentPaddingTop()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPlaceholderText()
com.google.android.material.internal.ForegroundLinearLayout: ForegroundLinearLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.bottomappbar.BottomAppBar: int getLeftInset()
com.google.android.material.appbar.AppBarLayout: android.graphics.drawable.Drawable getStatusBarForeground()
com.google.android.material.internal.NavigationMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.appcompat.widget.AbsActionBarView: int getContentHeight()
androidx.cardview.widget.CardView: boolean getUseCompatPadding()
com.google.android.material.textfield.MaterialAutoCompleteTextView: void setAdapter(android.widget.ListAdapter)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipStrokeColor()
com.google.android.material.card.MaterialCardView: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
com.google.android.material.chip.Chip: android.text.TextUtils$TruncateAt getEllipsize()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setFitsSystemWindows(boolean)
com.google.android.material.textview.MaterialTextView: MaterialTextView(android.content.Context,android.util.AttributeSet)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
com.google.android.material.floatingactionbutton.FloatingActionButton: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
androidx.constraintlayout.widget.Guideline: void setGuidelinePercent(float)
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: LinearLayoutCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
com.google.android.material.appbar.AppBarLayout: void setVisibility(int)
com.google.android.material.bottomappbar.BottomAppBar: androidx.appcompat.widget.ActionMenuView getActionMenuView()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.ChipGroup: void setCheckedId(int)
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
com.google.android.material.textfield.TextInputLayout: void setDefaultHintTextColor(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: void setElevation(float)
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
com.google.android.material.bottomappbar.BottomAppBar: void setFabCornerSize(float)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getSuffixTextView()
androidx.constraintlayout.widget.ConstraintLayout: int getMinWidth()
androidx.appcompat.widget.AppCompatSpinner: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type valueOf(java.lang.String)
com.google.android.material.chip.ChipGroup: ChipGroup(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$3: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.google.android.material.chip.Chip: java.lang.CharSequence getCloseIconContentDescription()
com.google.android.material.chip.Chip: void setCheckedIconTint(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(int)
com.google.android.material.chip.ChipGroup: void setSingleSelection(boolean)
com.google.android.material.textfield.TextInputLayout: void setErrorContentDescription(java.lang.CharSequence)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.cardview.widget.CardView: void setPreventCornerOverlap(boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior()
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setHelperTextTextAppearance(int)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: MaterialButton(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.NavigationMenuItemView: void setHorizontalPadding(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.constraintlayout.widget.Barrier: Barrier(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: AppCompatTextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.appcompat.widget.SearchView: void setInputType(int)
com.lockscreen.app.activities.SettingsActivity$SettingsFragment: SettingsActivity$SettingsFragment()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getActionTextColorAlpha()
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: BaseTransientBottomBar$SnackbarBaseLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatEditText: AppCompatEditText(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getStrokeColor()
com.google.android.material.card.MaterialCardView: int getStrokeColor()
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: AppCompatAutoCompleteTextView(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: android.graphics.drawable.Drawable getIcon()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setOnClickListener(android.view.View$OnClickListener)
androidx.constraintlayout.widget.Barrier: void setDpMargin(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconTintList(android.content.res.ColorStateList)
com.google.android.material.timepicker.ClockFaceView: ClockFaceView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ScrollingTabContainerView: void setAllowCollapse(boolean)
com.google.android.material.chip.ChipGroup: void setDividerDrawableHorizontal(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
com.google.android.material.card.MaterialCardView: void setChecked(boolean)
com.google.android.material.chip.Chip: float getChipStartPadding()
com.google.android.material.bottomappbar.BottomAppBar: void setFabCradleMargin(float)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.chip.Chip: void setChipIconResource(int)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
com.google.android.material.button.MaterialButton: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
com.google.android.material.card.MaterialCardView: android.graphics.RectF getBoundsAsRectF()
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.floatingactionbutton.FloatingActionButton: void setExpandedComponentIdHint(int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.ConstraintLayout: int getPaddingWidth()
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
com.google.android.material.textfield.TextInputLayout: void setHelperText(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setEndIconOnClickListener(android.view.View$OnClickListener)
com.google.android.material.appbar.ViewOffsetBehavior: ViewOffsetBehavior(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.VirtualLayout: void setVisibility(int)
com.google.android.material.appbar.AppBarLayout: int getDownNestedScrollRange()
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.cardview.widget.CardView: int getContentPaddingTop()
com.google.android.material.chip.Chip: void setTextEndPadding(float)
com.google.android.material.textfield.TextInputLayout: void setSuffixTextAppearance(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getStartIconContentDescription()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.CheckableImageButton: CheckableImageButton(android.content.Context,android.util.AttributeSet)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.FlowLayout: FlowLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.cardview.widget.CardView: void setCardBackgroundColor(int)
com.google.android.material.internal.BaselineLayout: BaselineLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior()
androidx.constraintlayout.widget.Barrier: void setAllowsGoneWidget(boolean)
com.google.android.material.chip.Chip: android.graphics.RectF getCloseIconTouchBounds()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.bottomappbar.BottomAppBar: int getBottomInset()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipIconTint()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setAnimationMode(int)
com.google.android.material.chip.Chip: void setCloseIconHovered(boolean)
