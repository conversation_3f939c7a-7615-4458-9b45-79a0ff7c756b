# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.3.0"
  }
  digests {
    sha256: "\032+lLc\376\r\365\224d\214\a\276\246d\362)\005\376\322|f\3344a/\320\304\322\275\207~"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.2.0"
  }
  digests {
    sha256: "\220)&+\335\316\021nm\002\276I\236J\375\272!\362L#\220\207\267k;W\327\351\213I\n6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.5.0"
  }
  digests {
    sha256: "+\'\227\022yV\211\006\234\373c\344\213:\266<2\245d\233\335\244LH.\270\370\034\241\247!a"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.3.1"
  }
  digests {
    sha256: "\335)OJh\234q\377\207\177\324\037;g\243\246/w`\324L\344 \346\023\017\037\303V\235\217\000"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\335wa[\323\335\'Z\373\021\266-\362[\256F\261\vJ\021|\323yC\257E\275\313\370uXR"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.3.1"
  }
  digests {
    sha256: "\025\204\217\265m\263/L|\334r\263$\0001\203\325*H\204\326\277\t\276p\212\307\365\207\3219\265"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.2.3"
  }
  digests {
    sha256: "\035\316\a\005\3034\246\262\357\0038$\030\334u\206\364\345~\3428\027&{@>\250\317\303l\202N"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.3.1"
  }
  digests {
    sha256: "\266\333L\'J\022\377\205\244t~\036fi\307\351\212\357\242W\032\316\235\037\032o\246\276A|\3508"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.1.0"
  }
  digests {
    sha256: "\326\v\276D\302\300\200\203\241|]\306x\246\326\264\320\242\326d\205\200\026\253\\\004\234\276\251\nc\267"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.3.1"
  }
  digests {
    sha256: "\227\023z\212\366\243\027v\241NHf\253\200\214|\ny\033HK\333\307\210\273\330>f@ud\300"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.3.1"
  }
  digests {
    sha256: "\345]8\303rF\017\n\003\231}\334\225\fg\"u\0214\017\327O\2064\331\235)e<\330\032\261"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.4"
  }
  digests {
    sha256: "\300#\300\253fdV\210R\204\330\350\205\031\247C\274\206<+.\377\271\'A\374x\234\275\261\r\264"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.2.0"
  }
  digests {
    sha256: "\330:\371H`\252\237d\313\334Q\364\a\226\247\317U\261\026\360\346\357\327R\350E\300\020L\213\026\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.0.0"
  }
  digests {
    sha256: "\262\031\322\265h\347\344\272SN\t\370\302\375$#C\337l\313\337\273\3518\204o]t\016k\v\021"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.3.0"
  }
  digests {
    sha256: "\262\341\231\311\023\233\022u3\335\352kJ\216\267o=\326\225W\273\205,\253- \203g\266\200\215\006"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.4"
  }
  digests {
    sha256: "0zy\244\241\314\377D$\234r\242\277\177G\332\t\372\033k\037\253*%\200\214\250\2118+s\216"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.4"
  }
  digests {
    sha256: "\234\241\237THp\223\001\307V4\210\357\224\033\351\337\245\\\203S\214\247\240Y\262\021>\203R{F"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.4.0"
  }
  digests {
    sha256: "\200\240\340*\277\212\212\214\276W\026\340j\310\f\326\203\204\v\237[\r/\031\242\242y\344\177(\225\356"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.1.1"
  }
  digests {
    sha256: "1}\313\303\202B\256\242\366&,\006\325\033\212\"\202~\230\225\231g\355\324\017\202`\n\025\313K\377"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.2.0"
  }
  digests {
    sha256: ":\227~wx\374\204\030t-8\204\t\332\253\247\352\217\352\210#\322\037\373\226\344\304#oqPp"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.2.0"
  }
  digests {
    sha256: "\312(\001\377\300iUZ\376\330\355\335\"\222\023\017CiVE+\310\273\2550\373V\370\344\343\202\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.sun.mail"
    artifactId: "android-mail"
    version: "1.6.7"
  }
  digests {
    sha256: "\022\304$\n\243\244\314\3327\304\233Nk\210\nyz!\340:O\214t!\241\265\036\r\265R\267\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.sun.mail"
    artifactId: "android-activation"
    version: "1.6.7"
  }
  digests {
    sha256: "(W\023\262\265I\363\202\362jW\221\236\315\334L\203\331uW7\226\237\267\300\006\311\0052+\262S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "18.0.0"
  }
  digests {
    sha256: "\213F\362\376?\240\020c\236\222\016\333\337\fB\221\200\222\350NE=\231f\261\255\221o\v\026rY"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "17.5.0"
  }
  digests {
    sha256: "\031\214\236!\025\365\316_\221\024\f\331\264\201\334md\335cJ\302\326\306RUg\334_\340\000e\313"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "17.5.0"
  }
  digests {
    sha256: "6#\001\300\332\034v\\\273\334\370\352\206kl\266+\3010\310m*\247\314\236\234\030\246\345\036\247\235"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "17.2.0"
  }
  digests {
    sha256: "\2411\321&\024]\376\207\336\004\372\220O\234\351\027S\262\323\'8Q\267\320\204fjq%W\222\250"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-places-placereport"
    version: "17.0.0"
  }
  digests {
    sha256: ",\177\326:\320/(\025\n\344\377\344a]\254}iMy\016,Fg\367w\256\334\216\340T\351)"
  }
  repo_index {
    value: 2
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 12
  library_dep_index: 8
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 2
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 1
}
library_dependencies {
  library_index: 4
  library_dep_index: 1
  library_dep_index: 5
}
library_dependencies {
  library_index: 5
  library_dep_index: 1
}
library_dependencies {
  library_index: 6
  library_dep_index: 1
}
library_dependencies {
  library_index: 7
  library_dep_index: 1
  library_dep_index: 8
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 15
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 14
  library_dep_index: 5
  library_dep_index: 4
  library_dep_index: 6
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 10
  library_dep_index: 14
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 20
  library_dep_index: 11
}
library_dependencies {
  library_index: 20
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 5
}
library_dependencies {
  library_index: 22
  library_dep_index: 8
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 8
}
library_dependencies {
  library_index: 24
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 8
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 18
}
library_dependencies {
  library_index: 27
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 28
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 27
  library_dep_index: 2
  library_dep_index: 32
  library_dep_index: 21
  library_dep_index: 16
  library_dep_index: 3
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 23
  library_dep_index: 39
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 8
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 34
  library_dep_index: 19
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 18
  library_dep_index: 8
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 8
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 37
  library_dep_index: 2
  library_dep_index: 8
}
library_dependencies {
  library_index: 40
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 16
  library_dep_index: 37
  library_dep_index: 1
  library_dep_index: 8
}
library_dependencies {
  library_index: 41
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 6
  library_dep_index: 20
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 11
}
library_dependencies {
  library_index: 42
  library_dep_index: 3
}
library_dependencies {
  library_index: 43
  library_dep_index: 3
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 49
}
library_dependencies {
  library_index: 47
  library_dep_index: 8
  library_dep_index: 2
  library_dep_index: 16
  library_dep_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 2
  library_dep_index: 16
}
library_dependencies {
  library_index: 49
  library_dep_index: 48
}
library_dependencies {
  library_index: 50
  library_dep_index: 48
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 27
  dependency_index: 29
  dependency_index: 40
  dependency_index: 41
  dependency_index: 44
  dependency_index: 45
  dependency_index: 46
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/public"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/gradle-plugin"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/google"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/central"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/jcenter"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/google"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/jcenter"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/public"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/central"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
