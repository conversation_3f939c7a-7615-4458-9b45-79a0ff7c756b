package com.lockscreen.app.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.lockscreen.app.services.LockScreenService;
import com.lockscreen.app.utils.PreferenceUtils;

public class BootCompletedReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            PreferenceUtils preferenceUtils = new PreferenceUtils(context);
            
            // 检查是否启用了开机自启动和锁屏功能
            if (preferenceUtils.isStartOnBootEnabled() && preferenceUtils.isLockScreenEnabled()) {
                // 启动锁屏服务
                Intent serviceIntent = new Intent(context, LockScreenService.class);
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    context.startForegroundService(serviceIntent);
                } else {
                    context.startService(serviceIntent);
                }
            }
        }
    }
}