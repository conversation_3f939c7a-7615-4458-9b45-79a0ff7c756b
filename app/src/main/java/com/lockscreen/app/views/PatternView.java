package com.lockscreen.app.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.lockscreen.app.R;

import java.util.ArrayList;
import java.util.List;

public class PatternView extends View {

    private static final int GRID_SIZE = 3;
    private static final int DOT_COUNT = GRID_SIZE * GRID_SIZE;
    
    private Paint dotPaint;
    private Paint linePaint;
    private Paint highlightPaint;
    
    private float dotRadius;
    private float cellSize;
    private float padding;
    
    private Point[] dots;
    private List<Integer> pattern;
    private Path patternPath;
    
    private boolean isDrawing;
    private float currentX;
    private float currentY;
    
    private OnPatternListener patternListener;

    public PatternView(Context context) {
        super(context);
        init();
    }

    public PatternView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PatternView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 初始化画笔
        dotPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        dotPaint.setColor(getResources().getColor(R.color.patternDot));
        
        linePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        linePaint.setColor(getResources().getColor(R.color.patternLine));
        linePaint.setStyle(Paint.Style.STROKE);
        linePaint.setStrokeWidth(8f);
        
        highlightPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        highlightPaint.setColor(getResources().getColor(R.color.patternHighlight));
        
        // 初始化路径和图案列表
        patternPath = new Path();
        pattern = new ArrayList<>();
        
        // 初始化点阵列
        dots = new Point[DOT_COUNT];
        for (int i = 0; i < DOT_COUNT; i++) {
            dots[i] = new Point();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        // 计算单元格大小和点半径
        float size = Math.min(w, h);
        cellSize = size / GRID_SIZE;
        padding = cellSize / 2;
        dotRadius = cellSize / 6;
        
        // 计算每个点的位置
        for (int i = 0; i < GRID_SIZE; i++) {
            for (int j = 0; j < GRID_SIZE; j++) {
                int index = i * GRID_SIZE + j;
                dots[index].x = padding + j * cellSize;
                dots[index].y = padding + i * cellSize;
            }
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        // 绘制所有点
        for (int i = 0; i < DOT_COUNT; i++) {
            if (pattern.contains(i)) {
                // 绘制已选中的点
                canvas.drawCircle(dots[i].x, dots[i].y, dotRadius, highlightPaint);
            } else {
                // 绘制未选中的点
                canvas.drawCircle(dots[i].x, dots[i].y, dotRadius, dotPaint);
            }
        }
        
        // 绘制已连接的线
        if (!pattern.isEmpty()) {
            patternPath.reset();
            patternPath.moveTo(dots[pattern.get(0)].x, dots[pattern.get(0)].y);
            
            for (int i = 1; i < pattern.size(); i++) {
                patternPath.lineTo(dots[pattern.get(i)].x, dots[pattern.get(i)].y);
            }
            
            // 如果正在绘制，连接到当前触摸点
            if (isDrawing) {
                patternPath.lineTo(currentX, currentY);
            }
            
            canvas.drawPath(patternPath, linePaint);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();
        
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 开始绘制新图案
                pattern.clear();
                isDrawing = true;
                checkDotHit(x, y);
                break;
                
            case MotionEvent.ACTION_MOVE:
                // 继续绘制图案
                if (isDrawing) {
                    currentX = x;
                    currentY = y;
                    checkDotHit(x, y);
                }
                break;
                
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // 完成图案绘制
                if (isDrawing) {
                    isDrawing = false;
                    if (patternListener != null && !pattern.isEmpty()) {
                        // 将图案转换为字符串并通知监听器
                        StringBuilder patternString = new StringBuilder();
                        for (int dot : pattern) {
                            patternString.append(dot + 1); // 转换为1-9的数字
                        }
                        patternListener.onPatternComplete(patternString.toString());
                    }
                }
                break;
        }
        
        invalidate();
        return true;
    }

    private void checkDotHit(float x, float y) {
        // 检查是否触碰到点
        for (int i = 0; i < DOT_COUNT; i++) {
            if (!pattern.contains(i)) { // 只检查未选中的点
                float dx = x - dots[i].x;
                float dy = y - dots[i].y;
                float distance = (float) Math.sqrt(dx * dx + dy * dy);
                
                if (distance < dotRadius * 2) {
                    // 触碰到点，添加到图案中
                    pattern.add(i);
                    break;
                }
            }
        }
    }

    // 清除当前图案
    public void clearPattern() {
        pattern.clear();
        invalidate();
    }

    // 设置图案完成监听器
    public void setOnPatternListener(OnPatternListener listener) {
        this.patternListener = listener;
    }

    // 图案监听器接口
    public interface OnPatternListener {
        void onPatternComplete(String pattern);
    }

    // 点类，用于存储点的坐标
    private static class Point {
        float x;
        float y;
    }
}