package com.lockscreen.app.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import com.lockscreen.app.R;

public class GridPasswordView extends LinearLayout {

    private Button[] numberButtons = new Button[9];
    private Button btnClear;
    
    private OnGridButtonClickListener gridButtonClickListener;
    private OnClearButtonClickListener clearButtonClickListener;

    public GridPasswordView(Context context) {
        super(context);
        init(context);
    }

    public GridPasswordView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public GridPasswordView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        // 加载布局
        View view = LayoutInflater.from(context).inflate(R.layout.layout_grid_password, this, true);
        
        // 初始化按钮
        numberButtons[0] = view.findViewById(R.id.btnGrid1);
        numberButtons[1] = view.findViewById(R.id.btnGrid2);
        numberButtons[2] = view.findViewById(R.id.btnGrid3);
        numberButtons[3] = view.findViewById(R.id.btnGrid4);
        numberButtons[4] = view.findViewById(R.id.btnGrid5);
        numberButtons[5] = view.findViewById(R.id.btnGrid6);
        numberButtons[6] = view.findViewById(R.id.btnGrid7);
        numberButtons[7] = view.findViewById(R.id.btnGrid8);
        numberButtons[8] = view.findViewById(R.id.btnGrid9);
        btnClear = view.findViewById(R.id.btnClear);
        
        // 设置按钮点击监听
        for (int i = 0; i < numberButtons.length; i++) {
            final int number = i + 1;
            numberButtons[i].setOnClickListener(v -> {
                if (gridButtonClickListener != null) {
                    gridButtonClickListener.onGridButtonClick(String.valueOf(number));
                }
            });
        }
        
        btnClear.setOnClickListener(v -> {
            if (clearButtonClickListener != null) {
                clearButtonClickListener.onClearButtonClick();
            }
        });
    }

    // 设置九宫格按钮点击监听器
    public void setOnGridButtonClickListener(OnGridButtonClickListener listener) {
        this.gridButtonClickListener = listener;
    }

    // 设置清除按钮点击监听器
    public void setOnClearButtonClickListener(OnClearButtonClickListener listener) {
        this.clearButtonClickListener = listener;
    }

    // 九宫格按钮点击监听器接口
    public interface OnGridButtonClickListener {
        void onGridButtonClick(String number);
    }

    // 清除按钮点击监听器接口
    public interface OnClearButtonClickListener {
        void onClearButtonClick();
    }
}