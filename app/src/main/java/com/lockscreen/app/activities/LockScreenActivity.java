package com.lockscreen.app.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ViewFlipper;

import androidx.appcompat.app.AppCompatActivity;

import com.lockscreen.app.R;
import com.lockscreen.app.utils.EmailUtils;
import com.lockscreen.app.utils.PreferenceUtils;
import com.lockscreen.app.utils.SecurityUtils;
import com.lockscreen.app.views.GridPasswordView;
import com.lockscreen.app.views.PatternView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LockScreenActivity extends AppCompatActivity {

    private ImageView ivBackground;
    private TextView tvTime;
    private TextView tvDate;
    private TextView tvUnlockHint;
    private ViewFlipper viewFlipper;
    
    // 九宫格密码解锁相关
    private View layoutGrid;
    private TextView tvPasswordDisplay;
    
    // 图案密码解锁相关
    private EditText etPassword;
    private PatternView patternView;
    private Button btnUnlock;
    
    private PreferenceUtils preferenceUtils;
    private String currentPassword = "";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置全屏和锁屏标志
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
                | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        
        setContentView(R.layout.activity_lock_screen);
        
        preferenceUtils = new PreferenceUtils(this);
        
        // 初始化视图
        initViews();
        
        // 设置背景图片
        setBackgroundImage();
        
        // 更新时间和日期
        updateTimeAndDate();
        
        // 根据解锁方式设置界面
        setupUnlockMethod();
    }
    
    private void initViews() {
        ivBackground = findViewById(R.id.ivBackground);
        tvTime = findViewById(R.id.clockTime);
        tvDate = findViewById(R.id.clockDate);
        tvUnlockHint = findViewById(R.id.tvUnlockHint);
        viewFlipper = findViewById(R.id.viewFlipper);
        
        // 九宫格密码解锁相关
        View gridPasswordLayout = findViewById(R.id.layoutGridPassword);
        // 使用layoutGrid替代gridPasswordView
        layoutGrid = gridPasswordLayout.findViewById(R.id.layoutGrid);
        tvPasswordDisplay = gridPasswordLayout.findViewById(R.id.tvPasswordDisplay);
        
        // 图案密码解锁相关
        View patternPasswordLayout = findViewById(R.id.layoutPatternPassword);
        etPassword = patternPasswordLayout.findViewById(R.id.etPassword);
        patternView = patternPasswordLayout.findViewById(R.id.patternView);
        btnUnlock = patternPasswordLayout.findViewById(R.id.btnUnlock);
    }
    
    private void setBackgroundImage() {
        String imageUri = preferenceUtils.getBackgroundImageUri();
        if (imageUri != null && !imageUri.isEmpty()) {
            try {
                ivBackground.setImageURI(Uri.parse(imageUri));
            } catch (Exception e) {
                // 如果设置失败，使用默认背景
                ivBackground.setImageResource(R.drawable.default_background);
            }
        } else {
            // 使用默认背景
            ivBackground.setImageResource(R.drawable.default_background);
        }
    }
    
    private void updateTimeAndDate() {
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 EEEE", Locale.CHINA);
        
        Date now = new Date();
        tvTime.setText(timeFormat.format(now));
        tvDate.setText(dateFormat.format(now));
    }
    
    private void setupUnlockMethod() {
        String unlockMethod = preferenceUtils.getUnlockMethod();
        
        if ("password_grid".equals(unlockMethod)) {
            // 九宫格密码解锁界面
            viewFlipper.setDisplayedChild(0);
            tvUnlockHint.setText(R.string.enter_password_grid);
            setupGridPasswordUnlock();
        } else {
            // 图案密码解锁界面
            viewFlipper.setDisplayedChild(1);
            tvUnlockHint.setText(R.string.enter_pattern_input);
            setupPatternPasswordUnlock();
        }
    }
    
    private void setupGridPasswordUnlock() {
        // 获取九宫格按钮
        View gridPasswordLayout = findViewById(R.id.layoutGridPassword);
        Button btnGrid1 = gridPasswordLayout.findViewById(R.id.btnGrid1);
        Button btnGrid2 = gridPasswordLayout.findViewById(R.id.btnGrid2);
        Button btnGrid3 = gridPasswordLayout.findViewById(R.id.btnGrid3);
        Button btnGrid4 = gridPasswordLayout.findViewById(R.id.btnGrid4);
        Button btnGrid5 = gridPasswordLayout.findViewById(R.id.btnGrid5);
        Button btnGrid6 = gridPasswordLayout.findViewById(R.id.btnGrid6);
        Button btnGrid7 = gridPasswordLayout.findViewById(R.id.btnGrid7);
        Button btnGrid8 = gridPasswordLayout.findViewById(R.id.btnGrid8);
        Button btnGrid9 = gridPasswordLayout.findViewById(R.id.btnGrid9);
        Button btnClear = gridPasswordLayout.findViewById(R.id.btnClear);

        // 设置数字按钮点击监听
        View.OnClickListener numberClickListener = v -> {
            Button button = (Button) v;
            String number = button.getText().toString();
            currentPassword += number;
            updatePasswordDisplay();

            // 检查密码长度，如果达到设定长度则验证
            if (currentPassword.length() >= 4) {
                verifyPassword();
            }
        };

        btnGrid1.setOnClickListener(numberClickListener);
        btnGrid2.setOnClickListener(numberClickListener);
        btnGrid3.setOnClickListener(numberClickListener);
        btnGrid4.setOnClickListener(numberClickListener);
        btnGrid5.setOnClickListener(numberClickListener);
        btnGrid6.setOnClickListener(numberClickListener);
        btnGrid7.setOnClickListener(numberClickListener);
        btnGrid8.setOnClickListener(numberClickListener);
        btnGrid9.setOnClickListener(numberClickListener);

        // 设置清除按钮点击监听
        btnClear.setOnClickListener(v -> {
            currentPassword = "";
            updatePasswordDisplay();
        });
    }
    
    private void setupPatternPasswordUnlock() {
        // 设置解锁按钮点击监听
        btnUnlock.setOnClickListener(v -> {
            String inputPassword = etPassword.getText().toString();
            if (!inputPassword.isEmpty()) {
                // 验证输入的密码与存储的图案是否匹配
                if (verifyPatternWithPassword(inputPassword)) {
                    unlockAndFinish();
                } else {
                    etPassword.setError(getString(R.string.wrong_password));
                }
            }
        });
        
        // 设置图案视图监听（如果可见）
        if (patternView.getVisibility() == View.VISIBLE) {
            patternView.setOnPatternListener(pattern -> {
                // 验证绘制的图案与存储的密码是否匹配
                if (verifyPasswordWithPattern(pattern)) {
                    unlockAndFinish();
                } else {
                    patternView.clearPattern();
                    // 显示错误提示
                }
            });
        }
    }
    
    private void updatePasswordDisplay() {
        // 更新密码显示，用点代替实际数字
        StringBuilder dots = new StringBuilder();
        for (int i = 0; i < currentPassword.length(); i++) {
            dots.append("● ");
        }
        tvPasswordDisplay.setText(dots.toString());
    }
    
    private void verifyPassword() {
        String savedPassword = preferenceUtils.getPassword();
        if (currentPassword.equals(savedPassword)) {
            preferenceUtils.resetUnlockFailCount();
            unlockAndFinish();
        } else {
            // 密码错误，清空并显示错误提示
            currentPassword = "";
            updatePasswordDisplay();
            tvPasswordDisplay.setError(getString(R.string.wrong_password));
            handleUnlockFail();
        }
    }
    
    private boolean verifyPatternWithPassword(String inputPassword) {
        // 验证输入的密码是否与存储的图案匹配
        String savedPattern = preferenceUtils.getPattern();
        boolean isCorrect = savedPattern.equals(inputPassword);
        if (isCorrect) {
            preferenceUtils.resetUnlockFailCount();
        } else {
            handleUnlockFail();
        }
        return isCorrect;
    }
    
    private boolean verifyPasswordWithPattern(String pattern) {
        // 验证绘制的图案是否与存储的密码匹配
        String savedPassword = preferenceUtils.getPassword();
        boolean isCorrect = savedPassword.equals(pattern);
        if (isCorrect) {
            preferenceUtils.resetUnlockFailCount();
        } else {
            handleUnlockFail();
        }
        return isCorrect;
    }
    
    private void handleUnlockFail() {
        // 增加解锁失败计数
        preferenceUtils.incrementUnlockFailCount();
        int failCount = preferenceUtils.getUnlockFailCount();
        
        // 如果失败次数达到3次，发送安全邮件
        if (failCount >= 3) {
            // 检查是否启用了邮箱安全告警
            if (preferenceUtils.isEmailRecoveryEnabled()) {
                sendSecurityEmail();
            }
            // 重置失败计数
            preferenceUtils.resetUnlockFailCount();
        }
    }
    
    private void sendSecurityEmail() {
        // 检查是否设置了安全邮箱
        String securityEmail = preferenceUtils.getSecurityEmail();
        String emailSender = preferenceUtils.getEmailSender();
        String emailPassword = preferenceUtils.getEmailPassword();
        String smtpServer = preferenceUtils.getSmtpServer();
        int smtpPort = preferenceUtils.getSmtpPort();
        
        if (securityEmail.isEmpty() || emailSender.isEmpty() || emailPassword.isEmpty() || smtpServer.isEmpty()) {
            return;
        }
        
        // 发送安全邮件
        EmailUtils.sendSecurityEmail(this, securityEmail, emailSender, emailPassword, smtpServer, smtpPort, null, null, null);
    }
    
    private void unlockAndFinish() {
        // 解锁成功，返回到主屏幕
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finish();
    }
    
    @Override
    public void onBackPressed() {
        // 禁用返回键
        // 不调用super.onBackPressed()
    }
}