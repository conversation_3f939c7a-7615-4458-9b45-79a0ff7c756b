package com.lockscreen.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;

import com.lockscreen.app.R;
import com.lockscreen.app.services.LockScreenService;
import com.lockscreen.app.utils.PreferenceUtils;

public class MainActivity extends AppCompatActivity {

    private SwitchCompat switchLockScreen;
    private Button btnSettings;
    private Button btnLockNow;
    private TextView tvStatus;
    private PreferenceUtils preferenceUtils;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        preferenceUtils = new PreferenceUtils(this);

        // 初始化视图
        initViews();
        // 设置监听器
        setupListeners();
        // 更新状态显示
        updateStatusText();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 更新开关状态
        switchLockScreen.setChecked(preferenceUtils.isLockScreenEnabled());
        // 更新状态显示
        updateStatusText();
    }

    private void initViews() {
        switchLockScreen = findViewById(R.id.switchLockScreen);
        btnSettings = findViewById(R.id.btnSettings);
        btnLockNow = findViewById(R.id.btnLockNow);
        tvStatus = findViewById(R.id.tvStatus);

        // 设置开关初始状态
        switchLockScreen.setChecked(preferenceUtils.isLockScreenEnabled());
    }

    private void setupListeners() {
        // 锁屏开关监听
        switchLockScreen.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferenceUtils.setLockScreenEnabled(isChecked);
            if (isChecked) {
                startLockScreenService();
            } else {
                stopLockScreenService();
            }
            updateStatusText();
        });

        // 设置按钮监听
        btnSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
            startActivity(intent);
        });

        // 立即锁屏按钮监听
        btnLockNow.setOnClickListener(v -> {
            if (preferenceUtils.isLockScreenEnabled()) {
                Intent intent = new Intent(MainActivity.this, LockScreenActivity.class);
                startActivity(intent);
            }
        });
    }

    private void startLockScreenService() {
        Intent intent = new Intent(this, LockScreenService.class);
        startService(intent);
    }

    private void stopLockScreenService() {
        Intent intent = new Intent(this, LockScreenService.class);
        stopService(intent);
    }

    private void updateStatusText() {
        boolean isEnabled = preferenceUtils.isLockScreenEnabled();
        tvStatus.setText("锁屏服务状态: " + (isEnabled ? "已启用" : "已禁用"));
    }
}