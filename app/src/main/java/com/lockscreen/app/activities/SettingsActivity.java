package com.lockscreen.app.activities;

import android.app.Activity;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.widget.Toast;

import androidx.annotation.NonNull;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceManager;

import com.lockscreen.app.R;
import com.lockscreen.app.utils.EmailUtils;
import com.lockscreen.app.utils.PreferenceUtils;
import com.lockscreen.app.utils.SecurityUtils;

public class SettingsActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        if (savedInstanceState == null) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.settings_container, new SettingsFragment())
                    .commit();
        }

        // 设置返回按钮
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(R.string.settings);
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    public static class SettingsFragment extends PreferenceFragmentCompat implements SharedPreferences.OnSharedPreferenceChangeListener {

        private static final int REQUEST_PICK_IMAGE = 1001;
        private static final int REQUEST_SET_PASSWORD = 1002;
        private static final int REQUEST_SET_PATTERN = 1003;
        private static final int REQUEST_CAMERA_PERMISSION = 1004;
        private static final int REQUEST_LOCATION_PERMISSION = 1005;

        private PreferenceUtils preferenceUtils;

        @Override
        public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
            setPreferencesFromResource(R.xml.preferences, rootKey);
            preferenceUtils = new PreferenceUtils(getActivity());

            // 设置背景图片选择
            Preference backgroundPref = findPreference("background");
            if (backgroundPref != null) {
                backgroundPref.setOnPreferenceClickListener(preference -> {
                    openImagePicker();
                    return true;
                });
            }

            // 设置密码
            Preference setPasswordPref = findPreference("set_password");
            if (setPasswordPref != null) {
                setPasswordPref.setOnPreferenceClickListener(preference -> {
                    showSetPasswordDialog();
                    return true;
                });
            }

            // 设置图案
            Preference setPatternPref = findPreference("set_pattern");
            if (setPatternPref != null) {
                setPatternPref.setOnPreferenceClickListener(preference -> {
                    showSetPatternDialog();
                    return true;
                });
            }
            
            // 测试邮件发送
            Preference testEmailPref = findPreference("test_email");
            if (testEmailPref != null) {
                testEmailPref.setOnPreferenceClickListener(preference -> {
                    testEmailSending();
                    return true;
                });
            }
        }

        @Override
        public void onResume() {
            super.onResume();
            getPreferenceManager().getSharedPreferences().registerOnSharedPreferenceChangeListener(this);
        }

        @Override
        public void onPause() {
            super.onPause();
            getPreferenceManager().getSharedPreferences().unregisterOnSharedPreferenceChangeListener(this);
        }

        @Override
        public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
            // 处理偏好设置变化
            if (key.equals("enable_lock_screen")) {
                boolean enabled = sharedPreferences.getBoolean(key, false);
                preferenceUtils.setLockScreenEnabled(enabled);
            } else if (key.equals("start_on_boot")) {
                boolean enabled = sharedPreferences.getBoolean(key, false);
                preferenceUtils.setStartOnBootEnabled(enabled);
            } else if (key.equals("unlock_method")) {
                String method = sharedPreferences.getString(key, "password_grid");
                preferenceUtils.setUnlockMethod(method);
            }
        }

        private void openImagePicker() {
            Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            startActivityForResult(intent, REQUEST_PICK_IMAGE);
        }

        private void showSetPasswordDialog() {
            // 创建密码设置对话框
            AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
            builder.setTitle(R.string.set_password);

            // 设置输入框
            final android.widget.EditText input = new android.widget.EditText(getActivity());
            input.setInputType(android.text.InputType.TYPE_CLASS_NUMBER | android.text.InputType.TYPE_NUMBER_VARIATION_PASSWORD);
            builder.setView(input);

            // 设置按钮
            builder.setPositiveButton(R.string.ok, (dialog, which) -> {
                String password = input.getText().toString();
                if (password.length() >= 4) {
                    preferenceUtils.setPassword(password);
                    Toast.makeText(getActivity(), R.string.password_set, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(getActivity(), R.string.password_too_short, Toast.LENGTH_SHORT).show();
                }
            });
            builder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.cancel());

            builder.show();
        }

        private void showSetPatternDialog() {
            // 创建图案设置对话框
            // 在实际应用中，这里应该启动一个专门的图案设置活动
            // 为了简化，这里使用一个简单的对话框
            AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
            builder.setTitle(R.string.set_pattern);

            // 设置输入框 (简化版，实际应该使用PatternView)
            final android.widget.EditText input = new android.widget.EditText(getActivity());
            input.setHint(R.string.pattern_hint);
            input.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
            builder.setView(input);

            // 设置按钮
            builder.setPositiveButton(R.string.ok, (dialog, which) -> {
                String pattern = input.getText().toString();
                if (pattern.length() >= 4 && pattern.length() <= 9) {
                    preferenceUtils.setPattern(pattern);
                    Toast.makeText(getActivity(), R.string.pattern_set, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(getActivity(), R.string.pattern_invalid, Toast.LENGTH_SHORT).show();
                }
            });
            builder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.cancel());

            builder.show();
        }

        @Override
        public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
            super.onActivityResult(requestCode, resultCode, data);

            if (requestCode == REQUEST_PICK_IMAGE && resultCode == Activity.RESULT_OK && data != null) {
                Uri selectedImage = data.getData();
                if (selectedImage != null) {
                    preferenceUtils.setBackgroundImageUri(selectedImage.toString());
                    Toast.makeText(getActivity(), R.string.background_set, Toast.LENGTH_SHORT).show();
                }
            }
        }
        
        @Override
        public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
            
            if (requestCode == REQUEST_CAMERA_PERMISSION) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 相机权限已授予，继续测试邮件发送
                    checkLocationPermission();
                } else {
                    Toast.makeText(getActivity(), R.string.toast_permission_required, Toast.LENGTH_SHORT).show();
                }
            } else if (requestCode == REQUEST_LOCATION_PERMISSION) {
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 位置权限已授予，继续测试邮件发送
                    sendTestEmail();
                } else {
                    Toast.makeText(getActivity(), R.string.toast_permission_required, Toast.LENGTH_SHORT).show();
                }
            }
        }
        
        private void testEmailSending() {
            // 检查是否已设置安全邮箱
            String securityEmail = preferenceUtils.getSecurityEmail();
            String emailSender = preferenceUtils.getEmailSender();
            String emailPassword = preferenceUtils.getEmailPassword();
            String smtpServer = preferenceUtils.getSmtpServer();
            
            if (securityEmail.isEmpty() || emailSender.isEmpty() || emailPassword.isEmpty() || smtpServer.isEmpty()) {
                Toast.makeText(getActivity(), "请先完成安全邮箱设置", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 检查相机权限
            if (!SecurityUtils.checkCameraPermission(getActivity())) {
                SecurityUtils.requestCameraPermission(getActivity());
                return;
            }
            
            // 检查位置权限
            checkLocationPermission();
        }
        
        private void checkLocationPermission() {
            if (!SecurityUtils.checkLocationPermission(getActivity())) {
                SecurityUtils.requestLocationPermission(getActivity());
            } else {
                sendTestEmail();
            }
        }
        
        private void sendTestEmail() {
            String securityEmail = preferenceUtils.getSecurityEmail();
            String emailSender = preferenceUtils.getEmailSender();
            String emailPassword = preferenceUtils.getEmailPassword();
            String smtpServer = preferenceUtils.getSmtpServer();
            int smtpPort = preferenceUtils.getSmtpPort();
            
            Toast.makeText(getActivity(), "正在发送测试邮件...", Toast.LENGTH_SHORT).show();
            
            EmailUtils.sendTestEmail(getActivity(), securityEmail, emailSender, emailPassword, smtpServer, smtpPort,
                    new EmailUtils.EmailCallback() {
                        @Override
                        public void onEmailSent(boolean success, String message) {
                            if (success) {
                                Toast.makeText(getActivity(), R.string.toast_email_test_success, Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(getActivity(), R.string.toast_email_test_failed + ": " + message, Toast.LENGTH_LONG).show();
                            }
                        }
                    });
        }
    }
}