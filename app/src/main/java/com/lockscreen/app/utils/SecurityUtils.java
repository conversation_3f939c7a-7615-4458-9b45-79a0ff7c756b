package com.lockscreen.app.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.location.Location;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;
import android.util.Size;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnSuccessListener;

import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class SecurityUtils {
    private static final String TAG = "SecurityUtils";
    private static final int CAMERA_REQUEST_CODE = 100;
    private static final int LOCATION_REQUEST_CODE = 101;

    public interface PhotoCaptureCallback {
        void onPhotoCaptured(Bitmap photo);
        void onError(String message);
    }

    public interface LocationCallback {
        void onLocationReceived(Location location);
        void onError(String message);
    }

    // 检查相机权限
    public static boolean checkCameraPermission(Context context) {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }

    // 请求相机权限
    public static void requestCameraPermission(Activity activity) {
        ActivityCompat.requestPermissions(activity, new String[]{Manifest.permission.CAMERA}, CAMERA_REQUEST_CODE);
    }

    // 检查位置权限
    public static boolean checkLocationPermission(Context context) {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    // 请求位置权限
    public static void requestLocationPermission(Activity activity) {
        ActivityCompat.requestPermissions(activity, new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
        }, LOCATION_REQUEST_CODE);
    }

    // 获取当前位置
    public static void getCurrentLocation(Context context, LocationCallback callback) {
        if (!checkLocationPermission(context)) {
            callback.onError("未授予位置权限");
            return;
        }

        FusedLocationProviderClient fusedLocationClient = LocationServices.getFusedLocationProviderClient(context);
        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        callback.onLocationReceived(location);
                    } else {
                        callback.onError("无法获取位置信息");
                    }
                })
                .addOnFailureListener(e -> callback.onError("获取位置失败: " + e.getMessage()));
    }

    // 使用前置摄像头拍照
    public static void takeFrontCameraPhoto(Context context, PhotoCaptureCallback callback) {
        if (!checkCameraPermission(context)) {
            callback.onError("未授予相机权限");
            return;
        }

        CameraManager cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
        try {
            // 查找前置摄像头
            String frontCameraId = null;
            for (String cameraId : cameraManager.getCameraIdList()) {
                CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(cameraId);
                Integer facing = characteristics.get(CameraCharacteristics.LENS_FACING);
                if (facing != null && facing == CameraCharacteristics.LENS_FACING_FRONT) {
                    frontCameraId = cameraId;
                    break;
                }
            }

            if (frontCameraId == null) {
                callback.onError("未找到前置摄像头");
                return;
            }

            // 获取相机特性
            CameraCharacteristics characteristics = cameraManager.getCameraCharacteristics(frontCameraId);
            Size[] jpegSizes = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                    .getOutputSizes(ImageFormat.JPEG);
            int width = 640;
            int height = 480;
            if (jpegSizes != null && jpegSizes.length > 0) {
                width = jpegSizes[0].getWidth();
                height = jpegSizes[0].getHeight();
            }

            // 创建ImageReader用于接收图像数据
            ImageReader reader = ImageReader.newInstance(width, height, ImageFormat.JPEG, 1);
            HandlerThread handlerThread = new HandlerThread("CameraBackground");
            handlerThread.start();
            Handler backgroundHandler = new Handler(handlerThread.getLooper());

            // 创建图像可用的监听器
            final Semaphore imageCaptureSemaphore = new Semaphore(1);
            final Bitmap[] capturedBitmap = new Bitmap[1];

            ImageReader.OnImageAvailableListener readerListener = imageReader -> {
                try (Image image = imageReader.acquireLatestImage()) {
                    if (image == null) return;

                    ByteBuffer buffer = image.getPlanes()[0].getBuffer();
                    byte[] bytes = new byte[buffer.capacity()];
                    buffer.get(bytes);

                    // 将字节数组转换为位图
                    Bitmap bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

                    // 旋转图像（前置摄像头通常需要镜像翻转）
                    Matrix matrix = new Matrix();
                    matrix.postRotate(270); // 可能需要根据设备调整旋转角度
                    matrix.postScale(-1, 1); // 水平镜像
                    capturedBitmap[0] = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                    bitmap.recycle();

                    imageCaptureSemaphore.release();
                } catch (Exception e) {
                    Log.e(TAG, "处理图像失败", e);
                }
            };

            reader.setOnImageAvailableListener(readerListener, backgroundHandler);

            // 打开相机
            final CameraDevice[] cameraDevice = new CameraDevice[1];
            final Semaphore cameraOpenCloseSemaphore = new Semaphore(1);

            try {
                cameraOpenCloseSemaphore.acquire();
            } catch (InterruptedException e) {
                callback.onError("相机操作被中断");
                return;
            }

            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                callback.onError("未授予相机权限");
                return;
            }

            cameraManager.openCamera(frontCameraId, new CameraDevice.StateCallback() {
                @Override
                public void onOpened(@NonNull CameraDevice camera) {
                    cameraDevice[0] = camera;
                    cameraOpenCloseSemaphore.release();
                    try {
                        // 创建CaptureRequest.Builder
                        CaptureRequest.Builder captureBuilder = camera.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);
                        captureBuilder.addTarget(reader.getSurface());

                        // 自动对焦
                        captureBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
                        // 自动曝光
                        captureBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);

                        // 创建CameraCaptureSession
                        camera.createCaptureSession(Arrays.asList(reader.getSurface()),
                                new CameraCaptureSession.StateCallback() {
                                    @Override
                                    public void onConfigured(@NonNull CameraCaptureSession session) {
                                        try {
                                            // 拍照
                                            session.capture(captureBuilder.build(), null, backgroundHandler);
                                        } catch (CameraAccessException e) {
                                            Log.e(TAG, "相机访问异常", e);
                                            callback.onError("相机访问异常: " + e.getMessage());
                                        }
                                    }

                                    @Override
                                    public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                                        callback.onError("相机配置失败");
                                    }
                                }, backgroundHandler);
                    } catch (CameraAccessException e) {
                        Log.e(TAG, "相机访问异常", e);
                        callback.onError("相机访问异常: " + e.getMessage());
                    }
                }

                @Override
                public void onDisconnected(@NonNull CameraDevice camera) {
                    cameraOpenCloseSemaphore.release();
                    camera.close();
                    cameraDevice[0] = null;
                }

                @Override
                public void onError(@NonNull CameraDevice camera, int error) {
                    cameraOpenCloseSemaphore.release();
                    camera.close();
                    cameraDevice[0] = null;
                    callback.onError("相机错误: " + error);
                }
            }, backgroundHandler);

            // 等待图像捕获完成
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    if (imageCaptureSemaphore.tryAcquire(3, TimeUnit.SECONDS)) {
                        // 图像捕获成功
                        if (capturedBitmap[0] != null) {
                            callback.onPhotoCaptured(capturedBitmap[0]);
                        } else {
                            callback.onError("图像捕获失败");
                        }
                    } else {
                        callback.onError("图像捕获超时");
                    }

                    // 关闭相机
                    if (cameraDevice[0] != null) {
                        cameraDevice[0].close();
                        cameraDevice[0] = null;
                    }

                    // 停止后台线程
                    handlerThread.quitSafely();
                } catch (InterruptedException e) {
                    callback.onError("图像捕获被中断");
                }
            }, 1000); // 给相机一些时间来初始化和拍照

        } catch (CameraAccessException e) {
            Log.e(TAG, "相机访问异常", e);
            callback.onError("相机访问异常: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "拍照时发生未知错误", e);
            callback.onError("拍照时发生未知错误: " + e.getMessage());
        }
    }
}