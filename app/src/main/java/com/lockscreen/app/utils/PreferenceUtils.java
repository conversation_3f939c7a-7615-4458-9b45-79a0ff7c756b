package com.lockscreen.app.utils;

import android.content.Context;
import android.content.SharedPreferences;

import androidx.preference.PreferenceManager;

public class PreferenceUtils {

    private static final String KEY_LOCK_SCREEN_ENABLED = "enable_lock_screen";
    private static final String KEY_START_ON_BOOT = "start_on_boot";
    private static final String KEY_UNLOCK_METHOD = "unlock_method";
    private static final String KEY_PASSWORD = "password";
    private static final String KEY_PATTERN = "pattern";
    private static final String KEY_BACKGROUND_IMAGE = "background_image";
    
    // 安全邮箱相关
    private static final String KEY_ENABLE_EMAIL_RECOVERY = "enable_email_recovery";
    private static final String KEY_SECURITY_EMAIL = "security_email";
    private static final String KEY_EMAIL_SENDER = "email_sender";
    private static final String KEY_EMAIL_PASSWORD = "email_password";
    private static final String KEY_SMTP_SERVER = "smtp_server";
    private static final String KEY_SMTP_PORT = "smtp_port";
    private static final String KEY_UNLOCK_FAIL_COUNT = "unlock_fail_count";

    private final SharedPreferences preferences;

    public PreferenceUtils(Context context) {
        preferences = PreferenceManager.getDefaultSharedPreferences(context);
    }

    // 锁屏启用状态
    public boolean isLockScreenEnabled() {
        return preferences.getBoolean(KEY_LOCK_SCREEN_ENABLED, false);
    }

    public void setLockScreenEnabled(boolean enabled) {
        preferences.edit().putBoolean(KEY_LOCK_SCREEN_ENABLED, enabled).apply();
    }

    // 开机自启动状态
    public boolean isStartOnBootEnabled() {
        return preferences.getBoolean(KEY_START_ON_BOOT, false);
    }

    public void setStartOnBootEnabled(boolean enabled) {
        preferences.edit().putBoolean(KEY_START_ON_BOOT, enabled).apply();
    }

    // 解锁方式
    public String getUnlockMethod() {
        return preferences.getString(KEY_UNLOCK_METHOD, "password_grid");
    }

    public void setUnlockMethod(String method) {
        preferences.edit().putString(KEY_UNLOCK_METHOD, method).apply();
    }

    // 密码
    public String getPassword() {
        return preferences.getString(KEY_PASSWORD, "1234");
    }

    public void setPassword(String password) {
        preferences.edit().putString(KEY_PASSWORD, password).apply();
    }

    // 图案
    public String getPattern() {
        return preferences.getString(KEY_PATTERN, "1234");
    }

    public void setPattern(String pattern) {
        preferences.edit().putString(KEY_PATTERN, pattern).apply();
    }

    // 背景图片URI
    public String getBackgroundImageUri() {
        return preferences.getString(KEY_BACKGROUND_IMAGE, "");
    }

    public void setBackgroundImageUri(String uri) {
        preferences.edit().putString(KEY_BACKGROUND_IMAGE, uri).apply();
    }
    
    // 安全邮箱相关方法
    public boolean isEmailRecoveryEnabled() {
        return preferences.getBoolean(KEY_ENABLE_EMAIL_RECOVERY, false);
    }
    
    public void setEmailRecoveryEnabled(boolean enabled) {
        preferences.edit().putBoolean(KEY_ENABLE_EMAIL_RECOVERY, enabled).apply();
    }
    
    public String getSecurityEmail() {
        return preferences.getString(KEY_SECURITY_EMAIL, "");
    }
    
    public void setSecurityEmail(String email) {
        preferences.edit().putString(KEY_SECURITY_EMAIL, email).apply();
    }
    
    public String getEmailSender() {
        return preferences.getString(KEY_EMAIL_SENDER, "");
    }
    
    public void setEmailSender(String email) {
        preferences.edit().putString(KEY_EMAIL_SENDER, email).apply();
    }
    
    public String getEmailPassword() {
        return preferences.getString(KEY_EMAIL_PASSWORD, "");
    }
    
    public void setEmailPassword(String password) {
        preferences.edit().putString(KEY_EMAIL_PASSWORD, password).apply();
    }
    
    public String getSmtpServer() {
        return preferences.getString(KEY_SMTP_SERVER, "");
    }
    
    public void setSmtpServer(String server) {
        preferences.edit().putString(KEY_SMTP_SERVER, server).apply();
    }
    
    public int getSmtpPort() {
        return Integer.parseInt(preferences.getString(KEY_SMTP_PORT, "465"));
    }
    
    public void setSmtpPort(int port) {
        preferences.edit().putString(KEY_SMTP_PORT, String.valueOf(port)).apply();
    }
    
    // 解锁失败计数
    public int getUnlockFailCount() {
        return preferences.getInt(KEY_UNLOCK_FAIL_COUNT, 0);
    }
    
    public void setUnlockFailCount(int count) {
        preferences.edit().putInt(KEY_UNLOCK_FAIL_COUNT, count).apply();
    }
    
    public void incrementUnlockFailCount() {
        int count = getUnlockFailCount();
        setUnlockFailCount(count + 1);
    }
    
    public void resetUnlockFailCount() {
        setUnlockFailCount(0);
    }
}