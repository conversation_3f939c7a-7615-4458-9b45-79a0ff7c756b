package com.lockscreen.app.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.location.Location;
import android.os.AsyncTask;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

public class EmailUtils {
    private static final String TAG = "EmailUtils";

    public interface EmailCallback {
        void onEmailSent(boolean success, String message);
    }

    public static void sendSecurityEmail(Context context, String recipientEmail, String senderEmail,
                                         String senderPassword, String smtpServer, int smtpPort,
                                         Bitmap photo, Location location, EmailCallback callback) {
        new SendEmailTask(context, recipientEmail, senderEmail, senderPassword, smtpServer, smtpPort,
                photo, location, callback).execute();
    }

    private static class SendEmailTask extends AsyncTask<Void, Void, Boolean> {
        private final Context context;
        private final String recipientEmail;
        private final String senderEmail;
        private final String senderPassword;
        private final String smtpServer;
        private final int smtpPort;
        private final Bitmap photo;
        private final Location location;
        private final EmailCallback callback;
        private String errorMessage;

        public SendEmailTask(Context context, String recipientEmail, String senderEmail,
                             String senderPassword, String smtpServer, int smtpPort,
                             Bitmap photo, Location location, EmailCallback callback) {
            this.context = context;
            this.recipientEmail = recipientEmail;
            this.senderEmail = senderEmail;
            this.senderPassword = senderPassword;
            this.smtpServer = smtpServer;
            this.smtpPort = smtpPort;
            this.photo = photo;
            this.location = location;
            this.callback = callback;
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                Properties props = new Properties();
                props.put("mail.smtp.auth", "true");
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.host", smtpServer);
                props.put("mail.smtp.port", String.valueOf(smtpPort));
                props.put("mail.smtp.socketFactory.port", String.valueOf(smtpPort));
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");

                Session session = Session.getInstance(props, new Authenticator() {
                    @Override
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(senderEmail, senderPassword);
                    }
                });

                Message message = new MimeMessage(session);
                message.setFrom(new InternetAddress(senderEmail));
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipientEmail));
                message.setSubject("手机解锁失败告警");

                // 创建多部分消息
                Multipart multipart = new MimeMultipart();

                // 添加文本部分
                BodyPart messageBodyPart = new MimeBodyPart();
                StringBuilder body = new StringBuilder();
                body.append("您的手机解锁失败三次，可能存在安全风险。\n\n");
                
                // 添加位置信息
                if (location != null) {
                    body.append("设备当前位置：\n");
                    body.append("纬度：").append(location.getLatitude()).append("\n");
                    body.append("经度：").append(location.getLongitude()).append("\n\n");
                } else {
                    body.append("无法获取设备位置信息\n\n");
                }

                body.append("此邮件由锁屏软件自动发送，请勿回复。");
                messageBodyPart.setText(body.toString());
                multipart.addBodyPart(messageBodyPart);

                // 添加照片附件
                if (photo != null) {
                    messageBodyPart = new MimeBodyPart();
                    File photoFile = createTempFileFromBitmap(photo);
                    if (photoFile != null) {
                        DataSource source = new FileDataSource(photoFile);
                        messageBodyPart.setDataHandler(new DataHandler(source));
                        messageBodyPart.setFileName("security_photo.jpg");
                        multipart.addBodyPart(messageBodyPart);
                    }
                }

                // 设置邮件内容
                message.setContent(multipart);

                // 发送邮件
                Transport.send(message);
                return true;
            } catch (MessagingException e) {
                Log.e(TAG, "发送邮件失败", e);
                errorMessage = e.getMessage();
                return false;
            } catch (Exception e) {
                Log.e(TAG, "发送邮件时发生未知错误", e);
                errorMessage = e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                callback.onEmailSent(success, success ? "邮件发送成功" : "邮件发送失败: " + errorMessage);
            }
        }

        private File createTempFileFromBitmap(Bitmap bitmap) {
            try {
                File cacheDir = context.getCacheDir();
                File tempFile = new File(cacheDir, "security_photo_" + new Date().getTime() + ".jpg");
                FileOutputStream fos = new FileOutputStream(tempFile);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fos);
                fos.flush();
                fos.close();
                return tempFile;
            } catch (Exception e) {
                Log.e(TAG, "创建临时文件失败", e);
                return null;
            }
        }
    }

    // 测试邮件发送功能
    public static void sendTestEmail(Context context, String recipientEmail, String senderEmail,
                                    String senderPassword, String smtpServer, int smtpPort,
                                    EmailCallback callback) {
        new SendTestEmailTask(context, recipientEmail, senderEmail, senderPassword, smtpServer, smtpPort, callback).execute();
    }

    private static class SendTestEmailTask extends AsyncTask<Void, Void, Boolean> {
        private final Context context;
        private final String recipientEmail;
        private final String senderEmail;
        private final String senderPassword;
        private final String smtpServer;
        private final int smtpPort;
        private final EmailCallback callback;
        private String errorMessage;

        public SendTestEmailTask(Context context, String recipientEmail, String senderEmail,
                               String senderPassword, String smtpServer, int smtpPort,
                               EmailCallback callback) {
            this.context = context;
            this.recipientEmail = recipientEmail;
            this.senderEmail = senderEmail;
            this.senderPassword = senderPassword;
            this.smtpServer = smtpServer;
            this.smtpPort = smtpPort;
            this.callback = callback;
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            try {
                Properties props = new Properties();
                props.put("mail.smtp.auth", "true");
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.host", smtpServer);
                props.put("mail.smtp.port", String.valueOf(smtpPort));
                props.put("mail.smtp.socketFactory.port", String.valueOf(smtpPort));
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");

                Session session = Session.getInstance(props, new Authenticator() {
                    @Override
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(senderEmail, senderPassword);
                    }
                });

                Message message = new MimeMessage(session);
                message.setFrom(new InternetAddress(senderEmail));
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipientEmail));
                message.setSubject("锁屏软件安全邮箱测试");
                message.setText("这是一封测试邮件，用于验证锁屏软件的安全邮箱功能是否正常。\n\n" +
                        "如果您收到此邮件，说明安全邮箱设置正确，当手机解锁失败三次后，将会自动发送安全告警邮件。\n\n" +
                        "此邮件由锁屏软件自动发送，请勿回复。");

                Transport.send(message);
                return true;
            } catch (MessagingException e) {
                Log.e(TAG, "发送测试邮件失败", e);
                errorMessage = e.getMessage();
                return false;
            } catch (Exception e) {
                Log.e(TAG, "发送测试邮件时发生未知错误", e);
                errorMessage = e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            if (callback != null) {
                callback.onEmailSent(success, success ? "测试邮件发送成功" : "测试邮件发送失败: " + errorMessage);
            }
        }
    }
}