<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- 常规设置 -->
    <PreferenceCategory
        android:title="@string/pref_category_general"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="enable_lock_screen"
            android:title="@string/pref_enable_lock_screen"
            android:summary="@string/pref_enable_lock_screen_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="start_on_boot"
            android:title="@string/pref_start_on_boot"
            android:summary="@string/pref_start_on_boot_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- 安全邮箱设置 -->
    <PreferenceCategory
        android:title="@string/pref_category_security_email"
        app:iconSpaceReserved="false">

        <EditTextPreference
            android:key="security_email"
            android:title="@string/pref_security_email"
            android:summary="@string/pref_security_email_summary"
            android:inputType="textEmailAddress"
            app:iconSpaceReserved="false" />
            
        <SwitchPreferenceCompat
            android:key="enable_email_recovery"
            android:title="@string/pref_enable_email_recovery"
            android:summary="@string/pref_enable_email_recovery_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="email_sender"
            android:title="@string/pref_email_sender"
            android:summary="@string/pref_email_sender_summary"
            android:inputType="textEmailAddress"
            android:dependency="enable_email_recovery"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="email_password"
            android:title="@string/pref_email_password"
            android:summary="@string/pref_email_password_summary"
            android:inputType="textPassword"
            android:dependency="enable_email_recovery"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="smtp_server"
            android:title="@string/pref_smtp_server"
            android:summary="@string/pref_smtp_server_summary"
            android:inputType="text"
            android:dependency="enable_email_recovery"
            app:iconSpaceReserved="false" />

        <EditTextPreference
            android:key="smtp_port"
            android:title="@string/pref_smtp_port"
            android:summary="@string/pref_smtp_port_summary"
            android:inputType="number"
            android:defaultValue="465"
            android:dependency="enable_email_recovery"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="test_email"
            android:title="@string/pref_test_email"
            android:summary="@string/pref_test_email_summary"
            android:dependency="enable_email_recovery"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- 解锁设置 -->
    <PreferenceCategory
        android:title="@string/pref_category_unlock"
        app:iconSpaceReserved="false">

        <ListPreference
            android:key="unlock_method"
            android:title="@string/pref_unlock_method"
            android:summary="@string/pref_unlock_method_summary"
            android:entries="@array/unlock_method_entries"
            android:entryValues="@array/unlock_method_values"
            android:defaultValue="password_grid"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="set_password"
            android:title="@string/pref_set_password"
            android:summary="@string/pref_set_password_summary"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="set_pattern"
            android:title="@string/pref_set_pattern"
            android:summary="@string/pref_set_pattern_summary"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- 外观设置 -->
    <PreferenceCategory
        android:title="@string/pref_category_appearance"
        app:iconSpaceReserved="false">

        <Preference
            android:key="background"
            android:title="@string/pref_background"
            android:summary="@string/pref_background_summary"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

</PreferenceScreen>