<resources>
    <string name="app_name">锁屏软件</string>
    
    <!-- 通知相关 -->
    <string name="notification_channel_name">锁屏服务</string>
    <string name="notification_channel_description">锁屏服务运行通知</string>
    <string name="notification_title">锁屏服务正在运行</string>
    <string name="notification_text">点击进入设置</string>
    
    <!-- 主界面 -->
    <string name="enable_lock_screen">启用锁屏</string>
    <string name="disable_lock_screen">禁用锁屏</string>
    <string name="settings">设置</string>
    <string name="lock_now">立即锁屏</string>
    
    <!-- 设置界面 -->
    <string name="settings_title">锁屏设置</string>
    <string name="pref_category_general">常规设置</string>
    <string name="pref_category_unlock">解锁设置</string>
    <string name="pref_category_appearance">外观设置</string>
    <string name="pref_category_security">安全设置</string>
    <string name="pref_category_security_email">安全邮箱设置</string>
    <string name="pref_enable_email_recovery">启用邮箱安全告警</string>
    <string name="pref_enable_email_recovery_summary">解锁失败三次后发送安全邮件</string>
    
    <string name="pref_enable_lock_screen">启用锁屏</string>
    <string name="pref_enable_lock_screen_summary">启用后将替代系统锁屏</string>
    
    <string name="pref_start_on_boot">开机自启动</string>
    <string name="pref_start_on_boot_summary">开机后自动启动锁屏服务</string>
    
    <string name="pref_unlock_method">解锁方式</string>
    <string name="pref_unlock_method_summary">选择锁屏解锁方式</string>
    
    <string name="pref_set_password">设置密码</string>
    <string name="pref_set_password_summary">设置数字密码</string>
    
    <string name="pref_set_pattern">设置图案</string>
    <string name="pref_set_pattern_summary">设置解锁图案</string>
    
    <string name="pref_background">锁屏背景</string>
    <string name="pref_background_summary">选择锁屏背景图片</string>
    
    <!-- 安全邮箱设置 -->
    <string name="pref_security_email">安全邮箱</string>
    <string name="pref_security_email_summary">设置解锁失败时接收安全邮件的邮箱</string>
    <string name="pref_email_sender">发件人邮箱</string>
    <string name="pref_email_sender_summary">设置用于发送安全邮件的邮箱</string>
    <string name="pref_email_password">发件人密码</string>
    <string name="pref_email_password_summary">设置发件人邮箱的密码或授权码</string>
    <string name="pref_smtp_server">SMTP服务器</string>
    <string name="pref_smtp_server_summary">设置SMTP服务器地址</string>
    <string name="pref_smtp_port">SMTP端口</string>
    <string name="pref_smtp_port_summary">设置SMTP服务器端口</string>
    <string name="pref_test_email">测试邮件发送</string>
    <string name="pref_test_email_summary">测试安全邮件发送功能是否正常</string>
    
    <!-- 锁屏界面 -->
    <string name="swipe_to_unlock">滑动解锁</string>
    <string name="enter_password">请输入密码</string>
    <string name="wrong_password">密码错误</string>
    <string name="clear">清除</string>
    <string name="draw_pattern">绘制图案</string>
    <string name="unlock_failed">解锁失败，请重试</string>
    
    <!-- 解锁方式 -->
    <string name="unlock_method_password_grid">九宫格密码解锁</string>
    <string name="unlock_method_pattern_input">图案密码解锁</string>
    <string name="enter_password_grid">请输入九宫格密码</string>
    <string name="enter_pattern_input">请绘制解锁图案</string>
    
    <!-- 对话框 -->
    <string name="dialog_set_password_title">设置密码</string>
    <string name="dialog_set_password_message">请输入4-9位数字密码</string>
    <string name="dialog_confirm_password_title">确认密码</string>
    <string name="dialog_confirm_password_message">请再次输入密码确认</string>
    <string name="dialog_set_pattern_title">设置图案</string>
    <string name="dialog_set_pattern_message">请绘制解锁图案</string>
    <string name="dialog_confirm_pattern_title">确认图案</string>
    <string name="dialog_confirm_pattern_message">请再次绘制图案确认</string>
    
    <!-- 按钮 -->
    <string name="ok">确定</string>
    <string name="cancel">取消</string>
    <string name="save">保存</string>
    
    <!-- 提示信息 -->
    <string name="toast_password_set">密码设置成功</string>
    <string name="toast_password_mismatch">两次输入的密码不一致</string>
    <string name="toast_pattern_set">图案设置成功</string>
    <string name="toast_pattern_mismatch">两次绘制的图案不一致</string>
    <string name="toast_lock_screen_enabled">锁屏已启用</string>
    <string name="toast_lock_screen_disabled">锁屏已禁用</string>
    <string name="toast_email_set">安全邮箱设置成功</string>
    <string name="toast_email_test_success">测试邮件发送成功</string>
    <string name="toast_email_test_failed">测试邮件发送失败</string>
    <string name="toast_permission_required">需要相机和位置权限</string>
    <string name="toast_unlock_failed">解锁失败，还有%d次机会</string>
    <string name="toast_security_email_sent">已发送安全邮件</string>
</resources>