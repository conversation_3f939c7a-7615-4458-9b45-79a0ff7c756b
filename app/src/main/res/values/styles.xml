<resources>
    <!-- 基础应用主题 -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- 锁屏主题 -->
    <style name="LockScreenTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowShowWallpaper">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>

    <!-- 九宫格按钮样式 -->
    <style name="GridButtonStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:background">@drawable/grid_button_background</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">24sp</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- 密码输入框样式 -->
    <style name="PasswordInputStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textColorHint">@android:color/darker_gray</item>
        <item name="android:backgroundTint">@android:color/white</item>
        <item name="android:maxLength">9</item>
        <item name="android:inputType">numberPassword</item>
    </style>
</resources>