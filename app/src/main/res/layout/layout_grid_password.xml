<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 密码显示区域 -->
    <TextView
        android:id="@+id/tvPasswordDisplay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/enter_password"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 密码点显示 -->
    <LinearLayout
        android:id="@+id/layoutPasswordDots"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPasswordDisplay">

        <!-- 密码点由代码动态添加 -->

    </LinearLayout>

    <!-- 九宫格按钮区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutGrid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutPasswordDots">

        <!-- 第一行 -->
        <Button
            android:id="@+id/btnGrid1"
            style="@style/GridButtonStyle"
            android:text="1"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid2"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btnGrid2"
            style="@style/GridButtonStyle"
            android:text="2"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid3"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid1"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btnGrid3"
            style="@style/GridButtonStyle"
            android:text="3"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid2"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 第二行 -->
        <Button
            android:id="@+id/btnGrid4"
            style="@style/GridButtonStyle"
            android:text="4"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid5"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid1" />

        <Button
            android:id="@+id/btnGrid5"
            style="@style/GridButtonStyle"
            android:text="5"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid6"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid4"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid2" />

        <Button
            android:id="@+id/btnGrid6"
            style="@style/GridButtonStyle"
            android:text="6"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid5"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid3" />

        <!-- 第三行 -->
        <Button
            android:id="@+id/btnGrid7"
            style="@style/GridButtonStyle"
            android:text="7"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid8"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid4" />

        <Button
            android:id="@+id/btnGrid8"
            style="@style/GridButtonStyle"
            android:text="8"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@+id/btnGrid9"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid7"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid5" />

        <Button
            android:id="@+id/btnGrid9"
            style="@style/GridButtonStyle"
            android:text="9"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btnGrid8"
            app:layout_constraintTop_toBottomOf="@+id/btnGrid6" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 清除按钮 -->
    <Button
        android:id="@+id/btnClear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/clear"
        android:textColor="@android:color/white"
        android:backgroundTint="@color/colorAccent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutGrid" />

</androidx.constraintlayout.widget.ConstraintLayout>