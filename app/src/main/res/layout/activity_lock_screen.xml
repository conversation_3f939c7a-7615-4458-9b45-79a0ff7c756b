<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.LockScreenActivity">

    <!-- 背景图片 -->
    <ImageView
        android:id="@+id/ivBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:contentDescription="@null" />

    <!-- 半透明遮罩 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000" />

    <!-- 时间显示 -->
    <TextClock
        android:id="@+id/clockTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="h:mm"
        android:format24Hour="HH:mm"
        android:textColor="@android:color/white"
        android:textSize="72sp"
        app:layout_constraintBottom_toTopOf="@+id/clockDate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- 日期显示 -->
    <TextClock
        android:id="@+id/clockDate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="EEEE, MMMM d"
        android:format24Hour="EEEE, MMMM d"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toTopOf="@+id/viewFlipper"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clockTime" />

    <!-- 解锁界面切换器 -->
    <ViewFlipper
        android:id="@+id/viewFlipper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clockDate">

        <!-- 九宫格密码解锁界面 -->
        <include
            android:id="@+id/layoutGridPassword"
            layout="@layout/layout_grid_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <!-- 图案密码解锁界面 -->
        <include
            android:id="@+id/layoutPatternPassword"
            layout="@layout/layout_pattern_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </ViewFlipper>

    <!-- 解锁提示文本 -->
    <TextView
        android:id="@+id/tvUnlockHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="@string/swipe_to_unlock"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>