# 锁屏应用优化记录

## 优化内容概述

本次优化主要包含三个方面：
1. 添加安全邮箱设置功能
2. 实现解锁失败后发送告警邮件功能
3. 优化编译速度，使用国内源下载依赖

## 详细优化内容

### 1. 安全邮箱设置功能

在设置页面添加了安全邮箱设置功能，包括以下设置项：

- **启用邮箱安全告警**：开关选项，控制是否启用安全邮件功能
- **安全邮箱地址**：接收告警邮件的邮箱地址
- **发件人邮箱**：用于发送告警邮件的邮箱地址
- **邮箱密码**：发件人邮箱的密码
- **SMTP服务器**：发件人邮箱的SMTP服务器地址
- **SMTP端口**：SMTP服务器端口
- **测试邮件发送**：测试邮件发送功能是否正常

相关文件修改：
- `app/src/main/res/xml/preferences.xml`：添加安全邮箱设置相关的偏好设置项
- `app/src/main/res/values/strings.xml`：添加安全邮箱设置相关的字符串资源
- `app/src/main/java/com/lockscreen/app/utils/PreferenceUtils.java`：添加安全邮箱设置相关的工具方法
- `app/src/main/java/com/lockscreen/app/activities/SettingsActivity.java`：添加测试邮件发送功能

### 2. 解锁失败告警功能

实现了在解锁失败三次后，自动发送包含前置摄像头拍摄的照片和手机位置信息的告警邮件功能：

- 添加了解锁失败计数功能
- 在密码和图案解锁失败时增加计数
- 当失败次数达到3次时，发送安全邮件
- 邮件包含前置摄像头拍摄的照片和手机位置信息

相关文件修改：
- `app/src/main/AndroidManifest.xml`：添加相机、位置和网络权限
- `app/src/main/java/com/lockscreen/app/activities/LockScreenActivity.java`：修改解锁逻辑，添加解锁失败处理
- `app/src/main/java/com/lockscreen/app/utils/EmailUtils.java`：新增，处理邮件发送功能
- `app/src/main/java/com/lockscreen/app/utils/SecurityUtils.java`：新增，处理相机拍照和位置获取功能

### 3. 编译优化

优化了编译速度，使用国内源下载依赖：

- 在`build.gradle`文件中添加了阿里云Maven镜像源
- 修改了`build.sh`脚本，添加了国内镜像源配置
- 添加了`--no-daemon`和`--parallel`参数提高编译效率

相关文件修改：
- `build.gradle`：添加阿里云Maven镜像源
- `app/build.gradle`：添加JavaMail和位置服务依赖
- `build.sh`：添加国内镜像源配置和编译优化参数

## 技术实现细节

### 安全邮箱设置

在`preferences.xml`中添加了安全邮箱设置类别，使用`EditTextPreference`和`SwitchPreferenceCompat`实现各项设置。在`PreferenceUtils`中添加了相应的getter和setter方法。

### 解锁失败告警

在`LockScreenActivity`中添加了`handleUnlockFail`方法，用于处理解锁失败事件。当解锁失败次数达到3次时，调用`sendSecurityEmail`方法发送告警邮件。

`EmailUtils`类使用JavaMail API实现邮件发送功能，`SecurityUtils`类使用Camera API和Location API实现拍照和获取位置功能。

### 编译优化

在`build.sh`脚本中添加了创建或更新`gradle.properties`文件的代码，配置阿里云镜像源。在`build.gradle`文件中添加了阿里云Maven镜像源配置。

## 测试方法

1. **安全邮箱设置测试**：
   - 进入设置页面，配置安全邮箱设置
   - 点击"测试邮件发送"，检查是否收到测试邮件

2. **解锁失败告警测试**：
   - 确保已启用邮箱安全告警并配置正确的邮箱信息
   - 连续三次输入错误的密码或图案
   - 检查是否收到包含照片和位置信息的告警邮件

3. **编译优化测试**：
   - 运行`./build.sh`脚本，观察编译速度是否提升
   - 检查是否使用了阿里云镜像源下载依赖