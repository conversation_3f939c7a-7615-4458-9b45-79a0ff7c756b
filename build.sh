#!/bin/bash

# 锁屏应用打包脚本
echo "开始构建锁屏应用..."

# 进入项目目录
cd "$(dirname "$0")"

# 设置Java环境变量
if [ -d "/usr/lib/jvm/java-11-openjdk" ]; then
    echo "使用Java 11..."
    export JAVA_HOME="/usr/lib/jvm/java-11-openjdk"
    export PATH="$JAVA_HOME/bin:$PATH"
elif [ -d "/usr/lib/jvm/java-17-openjdk" ]; then
    echo "使用Java 17..."
    export JAVA_HOME="/usr/lib/jvm/java-17-openjdk"
    export PATH="$JAVA_HOME/bin:$PATH"
else
    # 尝试查找系统中的Java 11或更高版本
    echo "尝试查找Java 11或更高版本..."
    JAVA_CANDIDATES=("/usr/lib/jvm/"* "/usr/java/"* "/opt/java/"*)
    for java_path in "${JAVA_CANDIDATES[@]}"; do
        if [ -d "$java_path" ] && [ -x "$java_path/bin/java" ]; then
            java_version=$($java_path/bin/java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
            if [ "$java_version" -ge 11 ]; then
                echo "找到Java $java_version: $java_path"
                export JAVA_HOME="$java_path"
                export PATH="$JAVA_HOME/bin:$PATH"
                break
            fi
        fi
    done
fi

# 验证Java版本
java -version

# 检查Gradle是否安装
GRADLE_INSTALLED=false

# 首先检查特定路径的Gradle 7.6
GRADLE_PATH="/root/gradle/gradle-7.6"
if [ -d "$GRADLE_PATH" ] && [ -f "$GRADLE_PATH/bin/gradle" ]; then
    echo "找到Gradle 7.6: $GRADLE_PATH"
    GRADLE_CMD="$GRADLE_PATH/bin/gradle"
    export GRADLE_HOME="$GRADLE_PATH"
    export PATH="$GRADLE_HOME/bin:$PATH"
    GRADLE_INSTALLED=true
fi

# 如果上面没找到，检查命令行中的gradle
if [ "$GRADLE_INSTALLED" = false ] && command -v gradle &> /dev/null; then
    echo "找到系统Gradle"
    GRADLE_CMD="gradle"
    GRADLE_INSTALLED=true
fi

# 如果仍未找到Gradle，尝试安装
if [ "$GRADLE_INSTALLED" = false ]; then
    echo "未找到Gradle，将尝试使用环境检测脚本安装..."
    
    # 检查环境检测脚本是否存在
    ENV_CHECK_SCRIPT="/home/<USER>/android_env_check.sh"
    if [ -f "$ENV_CHECK_SCRIPT" ]; then
        echo "找到环境检测脚本，开始安装Gradle..."
        # 执行环境检测脚本
        bash "$ENV_CHECK_SCRIPT"
        
        # 加载环境变量
        source ~/.bashrc
        
        # 如果环境变量中有GRADLE_HOME，直接使用
        if [ -n "$GRADLE_HOME" ] && [ -f "$GRADLE_HOME/bin/gradle" ]; then
            echo "使用GRADLE_HOME中的Gradle: $GRADLE_HOME"
            GRADLE_CMD="$GRADLE_HOME/bin/gradle"
        else
            # 尝试查找Gradle安装路径
            GRADLE_PATH="/root/gradle/gradle-7.6/bin/gradle"
            if [ -f "$GRADLE_PATH" ]; then
                echo "找到Gradle: $GRADLE_PATH"
                GRADLE_CMD="$GRADLE_PATH"
                # 临时设置环境变量
                export GRADLE_HOME="/root/gradle/gradle-7.6"
                export PATH="$GRADLE_HOME/bin:$PATH"
            else
                echo "错误: Gradle安装失败，请手动安装Gradle。"
                exit 1
            fi
        fi
        
        echo "Gradle安装成功，继续构建..."
    else
        echo "错误: 未找到环境检测脚本，请先安装Gradle。"
        exit 1
    fi
fi

# 清理项目
echo "清理项目..."
# 如果GRADLE_CMD已定义，则使用它，否则使用gradle命令
if [ -n "$GRADLE_CMD" ]; then
    "$GRADLE_CMD" clean
else
    gradle clean
fi

# 创建gradle.properties文件，使用国内镜像源
echo "配置国内镜像源..."
GRADLE_PROPERTIES="gradle.properties"

# 备份原有的gradle.properties文件
if [ -f "$GRADLE_PROPERTIES" ]; then
    cp "$GRADLE_PROPERTIES" "${GRADLE_PROPERTIES}.bak"
fi

# 创建或更新gradle.properties文件，添加国内镜像源配置
cat > "$GRADLE_PROPERTIES" << EOF
# 使用阿里云镜像
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.enableJetifier=true

# 阿里云镜像
repositoriesMode=fail_on_project_repos
repositoryUrl=https://maven.aliyun.com/repository/public
repositoriesUrl=https://maven.aliyun.com/repository/public
repositoriesUsername=
repositoriesPassword=

# 阿里云镜像配置
systemProp.org.gradle.internal.publish.checksums.insecure=true
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=
EOF

echo "国内镜像源配置完成"

# 设置Android SDK路径
echo "设置Android SDK路径..."
if [ -d "/root/Android/Sdk" ]; then
    export ANDROID_HOME="/root/Android/Sdk"
    export PATH="$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$PATH"
    echo "Android SDK路径: $ANDROID_HOME"
    echo "可用的SDK平台:"
    ls -la "$ANDROID_HOME/platforms"
fi

# 构建APK
echo "构建APK..."
if [ -n "$GRADLE_CMD" ]; then
    "$GRADLE_CMD" assembleRelease --no-daemon --parallel
else
    gradle assembleRelease --no-daemon --parallel
fi

# 检查构建结果
APK_PATH="app/build/outputs/apk/release/app-release-unsigned.apk"
if [ -f "$APK_PATH" ]; then
    echo "APK构建成功: $APK_PATH"
    
    # 创建输出目录
    OUTPUT_DIR="output"
    mkdir -p "$OUTPUT_DIR"
    
    # 复制APK到输出目录
    cp "$APK_PATH" "$OUTPUT_DIR/LockScreenApp.apk"
    echo "APK已复制到: $OUTPUT_DIR/LockScreenApp.apk"
    
    echo "构建完成！"
    echo "注意: 这是一个未签名的APK，如需安装到设备上，请先进行签名。"
    echo "可以使用以下命令进行签名:"
    echo "  1. 创建密钥: keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000"
    echo "  2. 签名APK: jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore $OUTPUT_DIR/LockScreenApp.apk alias_name"
    echo "  3. 优化APK: zipalign -v 4 $OUTPUT_DIR/LockScreenApp.apk $OUTPUT_DIR/LockScreenApp-aligned.apk"
    
    exit 0
else
    echo "错误: APK构建失败，请检查日志获取详细信息。"
    exit 1
fi