#!/bin/bash

# 锁屏应用打包脚本 v2.0
# 支持自动签名和完整的APK构建流程
echo "开始构建锁屏应用..."

# 进入项目目录
cd "$(dirname "$0")"

# 配置参数
APP_NAME="LockScreenApp"
VERSION="0.2"
KEYSTORE_NAME="lockscreen-release-key.keystore"
KEY_ALIAS="lockscreen"
KEYSTORE_PASSWORD="lockscreen123456"
KEY_PASSWORD="lockscreen123456"

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置Java环境变量
if [ -d "/usr/lib/jvm/java-11-openjdk" ]; then
    echo "使用Java 11..."
    export JAVA_HOME="/usr/lib/jvm/java-11-openjdk"
    export PATH="$JAVA_HOME/bin:$PATH"
elif [ -d "/usr/lib/jvm/java-17-openjdk" ]; then
    echo "使用Java 17..."
    export JAVA_HOME="/usr/lib/jvm/java-17-openjdk"
    export PATH="$JAVA_HOME/bin:$PATH"
else
    # 尝试查找系统中的Java 11或更高版本
    echo "尝试查找Java 11或更高版本..."
    JAVA_CANDIDATES=("/usr/lib/jvm/"* "/usr/java/"* "/opt/java/"*)
    for java_path in "${JAVA_CANDIDATES[@]}"; do
        if [ -d "$java_path" ] && [ -x "$java_path/bin/java" ]; then
            java_version=$($java_path/bin/java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
            if [ "$java_version" -ge 11 ]; then
                echo "找到Java $java_version: $java_path"
                export JAVA_HOME="$java_path"
                export PATH="$JAVA_HOME/bin:$PATH"
                break
            fi
        fi
    done
fi

# 验证Java版本
java -version

# 检查Gradle是否安装
GRADLE_INSTALLED=false

# 首先检查特定路径的Gradle 7.6
GRADLE_PATH="/root/gradle/gradle-7.6"
if [ -d "$GRADLE_PATH" ] && [ -f "$GRADLE_PATH/bin/gradle" ]; then
    echo "找到Gradle 7.6: $GRADLE_PATH"
    GRADLE_CMD="$GRADLE_PATH/bin/gradle"
    export GRADLE_HOME="$GRADLE_PATH"
    export PATH="$GRADLE_HOME/bin:$PATH"
    GRADLE_INSTALLED=true
fi

# 如果上面没找到，检查命令行中的gradle
if [ "$GRADLE_INSTALLED" = false ] && command -v gradle &> /dev/null; then
    echo "找到系统Gradle"
    GRADLE_CMD="gradle"
    GRADLE_INSTALLED=true
fi

# 如果仍未找到Gradle，尝试安装
if [ "$GRADLE_INSTALLED" = false ]; then
    echo "未找到Gradle，将尝试使用环境检测脚本安装..."
    
    # 检查环境检测脚本是否存在
    ENV_CHECK_SCRIPT="/home/<USER>/android_env_check.sh"
    if [ -f "$ENV_CHECK_SCRIPT" ]; then
        echo "找到环境检测脚本，开始安装Gradle..."
        # 执行环境检测脚本
        bash "$ENV_CHECK_SCRIPT"
        
        # 加载环境变量
        source ~/.bashrc
        
        # 如果环境变量中有GRADLE_HOME，直接使用
        if [ -n "$GRADLE_HOME" ] && [ -f "$GRADLE_HOME/bin/gradle" ]; then
            echo "使用GRADLE_HOME中的Gradle: $GRADLE_HOME"
            GRADLE_CMD="$GRADLE_HOME/bin/gradle"
        else
            # 尝试查找Gradle安装路径
            GRADLE_PATH="/root/gradle/gradle-7.6/bin/gradle"
            if [ -f "$GRADLE_PATH" ]; then
                echo "找到Gradle: $GRADLE_PATH"
                GRADLE_CMD="$GRADLE_PATH"
                # 临时设置环境变量
                export GRADLE_HOME="/root/gradle/gradle-7.6"
                export PATH="$GRADLE_HOME/bin:$PATH"
            else
                echo "错误: Gradle安装失败，请手动安装Gradle。"
                exit 1
            fi
        fi
        
        echo "Gradle安装成功，继续构建..."
    else
        echo "错误: 未找到环境检测脚本，请先安装Gradle。"
        exit 1
    fi
fi

# 清理项目
echo "清理项目..."
# 如果GRADLE_CMD已定义，则使用它，否则使用gradle命令
if [ -n "$GRADLE_CMD" ]; then
    "$GRADLE_CMD" clean
else
    gradle clean
fi

# 创建gradle.properties文件，使用国内镜像源
echo "配置国内镜像源..."
GRADLE_PROPERTIES="gradle.properties"

# 备份原有的gradle.properties文件
if [ -f "$GRADLE_PROPERTIES" ]; then
    cp "$GRADLE_PROPERTIES" "${GRADLE_PROPERTIES}.bak"
fi

# 创建或更新gradle.properties文件，添加国内镜像源配置
cat > "$GRADLE_PROPERTIES" << EOF
# 使用阿里云镜像
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.enableJetifier=true

# 阿里云镜像
repositoriesMode=fail_on_project_repos
repositoryUrl=https://maven.aliyun.com/repository/public
repositoriesUrl=https://maven.aliyun.com/repository/public
repositoriesUsername=
repositoriesPassword=

# 阿里云镜像配置
systemProp.org.gradle.internal.publish.checksums.insecure=true
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=
EOF

echo "国内镜像源配置完成"

# 设置Android SDK路径
echo "设置Android SDK路径..."
if [ -d "/root/Android/Sdk" ]; then
    export ANDROID_HOME="/root/Android/Sdk"
    export PATH="$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$PATH"
    echo "Android SDK路径: $ANDROID_HOME"
    echo "可用的SDK平台:"
    ls -la "$ANDROID_HOME/platforms"
fi

# 构建APK
echo "构建APK..."
if [ -n "$GRADLE_CMD" ]; then
    "$GRADLE_CMD" assembleRelease --no-daemon --parallel
else
    gradle assembleRelease --no-daemon --parallel
fi

# 创建密钥库函数
create_keystore() {
    print_info "创建签名密钥库..."

    if [ -f "$KEYSTORE_NAME" ]; then
        print_warning "密钥库已存在: $KEYSTORE_NAME"
        return 0
    fi

    # 创建密钥库
    keytool -genkey -v \
        -keystore "$KEYSTORE_NAME" \
        -alias "$KEY_ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -validity 10000 \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        -dname "CN=LockScreen App, OU=Development, O=LockScreen, L=Beijing, S=Beijing, C=CN"

    if [ $? -eq 0 ]; then
        print_success "密钥库创建成功: $KEYSTORE_NAME"
        return 0
    else
        print_error "密钥库创建失败"
        return 1
    fi
}

# 签名APK函数
sign_apk() {
    local unsigned_apk="$1"
    local signed_apk="$2"

    print_info "开始签名APK..."

    # 检查密钥库是否存在
    if [ ! -f "$KEYSTORE_NAME" ]; then
        print_error "密钥库不存在: $KEYSTORE_NAME"
        return 1
    fi

    # 复制未签名APK
    cp "$unsigned_apk" "$signed_apk"

    # 使用jarsigner签名
    jarsigner -verbose \
        -sigalg SHA256withRSA \
        -digestalg SHA-256 \
        -keystore "$KEYSTORE_NAME" \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        "$signed_apk" \
        "$KEY_ALIAS"

    if [ $? -eq 0 ]; then
        print_success "APK签名成功"
        return 0
    else
        print_error "APK签名失败"
        return 1
    fi
}

# 优化APK函数
optimize_apk() {
    local signed_apk="$1"
    local optimized_apk="$2"

    print_info "开始优化APK..."

    # 检查zipalign工具
    ZIPALIGN_CMD=""
    if [ -n "$ANDROID_HOME" ] && [ -f "$ANDROID_HOME/build-tools/30.0.3/zipalign" ]; then
        ZIPALIGN_CMD="$ANDROID_HOME/build-tools/30.0.3/zipalign"
    elif [ -n "$ANDROID_HOME" ] && [ -f "$ANDROID_HOME/build-tools/33.0.0/zipalign" ]; then
        ZIPALIGN_CMD="$ANDROID_HOME/build-tools/33.0.0/zipalign"
    elif command -v zipalign &> /dev/null; then
        ZIPALIGN_CMD="zipalign"
    else
        print_warning "未找到zipalign工具，跳过APK优化"
        cp "$signed_apk" "$optimized_apk"
        return 0
    fi

    # 执行zipalign优化
    "$ZIPALIGN_CMD" -v 4 "$signed_apk" "$optimized_apk"

    if [ $? -eq 0 ]; then
        print_success "APK优化成功"
        return 0
    else
        print_error "APK优化失败"
        cp "$signed_apk" "$optimized_apk"
        return 1
    fi
}

# 验证APK签名函数
verify_apk() {
    local apk_file="$1"

    print_info "验证APK签名..."

    jarsigner -verify -verbose -certs "$apk_file"

    if [ $? -eq 0 ]; then
        print_success "APK签名验证成功"
        return 0
    else
        print_error "APK签名验证失败"
        return 1
    fi
}

# 检查构建结果并进行签名
APK_PATH="app/build/outputs/apk/release/app-release-unsigned.apk"
if [ -f "$APK_PATH" ]; then
    print_success "APK构建成功: $APK_PATH"

    # 创建输出目录
    OUTPUT_DIR="output"
    mkdir -p "$OUTPUT_DIR"

    # 定义输出文件名
    UNSIGNED_APK="$OUTPUT_DIR/${APP_NAME}-v${VERSION}-unsigned.apk"
    SIGNED_APK="$OUTPUT_DIR/${APP_NAME}-v${VERSION}-signed.apk"
    FINAL_APK="$OUTPUT_DIR/${APP_NAME}-v${VERSION}.apk"

    # 复制未签名APK
    cp "$APK_PATH" "$UNSIGNED_APK"
    print_info "未签名APK已复制到: $UNSIGNED_APK"

    # 创建密钥库
    if create_keystore; then
        # 签名APK
        if sign_apk "$UNSIGNED_APK" "$SIGNED_APK"; then
            # 优化APK
            if optimize_apk "$SIGNED_APK" "$FINAL_APK"; then
                # 验证签名
                if verify_apk "$FINAL_APK"; then
                    # 获取APK信息
                    APK_SIZE=$(du -h "$FINAL_APK" | cut -f1)

                    print_success "==================== 构建完成 ===================="
                    print_success "应用名称: $APP_NAME"
                    print_success "版本号: $VERSION"
                    print_success "最终APK: $FINAL_APK"
                    print_success "文件大小: $APK_SIZE"
                    print_success "签名状态: 已签名并优化"
                    print_success "=================================================="

                    # 清理中间文件
                    rm -f "$SIGNED_APK"

                    # 创建兼容性链接
                    ln -sf "$(basename "$FINAL_APK")" "$OUTPUT_DIR/LockScreenApp.apk"

                    exit 0
                fi
            fi
        fi
    fi

    # 如果签名失败，至少提供未签名版本
    print_warning "签名过程失败，提供未签名版本"
    cp "$UNSIGNED_APK" "$OUTPUT_DIR/LockScreenApp.apk"
    print_info "未签名APK可用: $OUTPUT_DIR/LockScreenApp.apk"
    exit 0

else
    print_error "APK构建失败，请检查日志获取详细信息。"
    exit 1
fi